<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>客户档案管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h2 class="mb-4">客户档案管理</h2>
        <form id="customerForm" class="mb-3">
            <input type="hidden" id="id">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <input type="text" class="form-control" id="customerName" placeholder="客户名称">
                </div>
                <div class="col-md-4 mb-3">
                    <input type="text" class="form-control" id="contactPerson" placeholder="联系人">
                </div>
                <div class="col-md-4 mb-3">
                    <input type="text" class="form-control" id="phone" placeholder="联系电话">
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <input type="text" class="form-control" id="email" placeholder="邮箱">
                </div>
                <div class="col-md-4 mb-3">
                    <select class="form-select" id="salesManager">
                        <option value="">请选择销售负责人</option>
                        <option value="张三">张三</option>
                        <option value="李四">李四</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">保存</button>
                </div>
            </div>
        </form>

        <table class="table table-striped">
            <thead>
                <tr>
                    <th>客户名称</th>
                    <th>联系人</th>
                    <th>联系电话</th>
                    <th>销售负责人</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="customerList">
            </tbody>
        </table>

        <button class="btn btn-danger" id="deleteSelected">删除选中</button>
        <button class="btn btn-secondary" id="exportData">导出数据</button>
    </div>

    <script>
        // 表格初始化
        function loadCustomers() {
            $.post('/customer/info', {id: ''}, function(res) {
                $('#customerList').empty();
                res.data.forEach(item => {
                    $('#customerList').append(`
                        <tr>
                            <td>${item.customerName}</td>
                            <td>${item.contactPerson}</td>
                            <td>${item.phone}</td>
                            <td>${item.salesManager}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary edit-btn" data-id="${item.id}">编辑</button>
                                <input type="checkbox" class="delete-checkbox" data-id="${item.id}">
                            </td>
                        </tr>
                    `);
                });
            });
        }

        // 表单提交
        $('#customerForm').submit(function(e) {
            e.preventDefault();
            const data = {
                id: $('#id').val(),
                customerName: $('#customerName').val(),
                contactPerson: $('#contactPerson').val(),
                phone: $('#phone').val(),
                email: $('#email').val(),
                salesManager: $('#salesManager').val()
            };

            if (data.id) {
                // 编辑操作
                $.post('/customer/update', data, function(res) {
                    if (res.success) {
                        alert('更新成功');
                        loadCustomers();
                    }
                });
            } else {
                // 新增操作
                $.post('/customer/import', data, function(res) {
                    if (res.success) {
                        alert('新增成功');
                        loadCustomers();
                        $('#customerForm')[0].reset();
                    }
                });
            }
        });

        // 编辑按钮点击
        $(document).on('click', '.edit-btn', function() {
            const id = $(this).data('id');
            // 调用/info接口获取完整数据
            $.post('/customer/info', {id}, function(res) {
                const data = res.data;
                $('#id').val(data.id);
                $('#customerName').val(data.customerName);
                $('#contactPerson').val(data.contactPerson);
                $('#phone').val(data.phone);
                $('#email').val(data.email);
                $('#salesManager').val(data.salesManager);
            });
        });

        // 删除选中
        $('#deleteSelected').click(function() {
            const ids = [];
            $('.delete-checkbox:checked').each(function() {
                ids.push($(this).data('id'));
            });

            if (ids.length === 0) {
                alert('请至少选择一个客户');
                return;
            }

            if (confirm('确定要删除选中的客户吗？')) {
                $.post('/customer/delete', {ids}, function(res) {
                    if (res.success) {
                        alert('删除成功');
                        loadCustomers();
                    }
                });
            }
        });

        // 导出数据
        $('#exportData').click(function() {
            window.location.href = '/customer/export';
        });

        // 初始化加载
        loadCustomers();
    </script>
</body>
</html>