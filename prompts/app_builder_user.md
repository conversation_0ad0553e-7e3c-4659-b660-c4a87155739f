# APP构建用户提示词模板

## 基础模板

请为我创建一个名为"{name}"的Web应用。

**应用描述**：{description}

**具体需求**：{requirements}

## 技术要求

### 前端技术栈
- 使用Bootstrap 5.3.0 CSS框架
- 使用Font Awesome 6.4.0图标库  
- JavaScript使用原生ES6+或Vue.js 3.x
- 如需HTTP请求，使用Fetch API或Axios
- 所有外部资源使用CDN链接

### API调用规范
- 所有API调用都需要通过代理服务：`/apps/api/proxy`
- 代理请求格式：
{request_format}

### 可用API接口
{api_list}

## 设计要求

### 用户界面
1. **现代化设计**：采用简洁、现代的设计风格
2. **响应式布局**：适配桌面端、平板和移动端
3. **用户体验**：交互流畅、反馈及时、操作直观
4. **视觉层次**：合理的信息架构和视觉层次
5. **品牌一致性**：保持设计风格的一致性

### 功能特性
1. **核心功能**：确保主要功能完整可用
2. **错误处理**：完善的错误提示和异常处理
3. **数据验证**：前端数据验证和格式检查
4. **性能优化**：合理的加载策略和性能优化
5. **可访问性**：支持键盘导航和屏幕阅读器

## 代码规范

### HTML结构
- 使用语义化HTML5标签
- 合理的文档结构和层次
- 包含必要的meta标签和SEO优化

### CSS样式
- 遵循BEM命名规范
- 使用CSS变量管理主题色彩
- 实现流畅的动画和过渡效果
- 移动优先的响应式设计

### JavaScript代码
- 使用现代ES6+语法
- 模块化和组件化开发
- 事件处理和状态管理
- 异步操作和错误处理

## 输出格式

请生成完整的HTML页面代码，确保：

1. **完整性**：包含`<!DOCTYPE html>`声明和完整的HTML结构
2. **功能性**：所有功能都能正常工作
3. **美观性**：界面设计美观，用户体验良好
4. **可维护性**：代码结构清晰，注释完整
5. **兼容性**：支持主流浏览器和设备

## 特殊说明

{iteration_note}

请直接返回HTML代码，不需要额外的解释说明。代码应该可以直接在浏览器中运行。

---

**注意**：请确保生成的代码符合所有技术要求和设计规范，提供完整可用的Web应用程序。
