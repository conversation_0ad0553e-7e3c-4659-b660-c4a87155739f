<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RestAgent{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Prism.js for code highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: 100vh;
        }
        .chat-container {
            height: 70vh;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #fff;
        }
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
        }
        .message.user {
            background-color: #e3f2fd;
            margin-left: 2rem;
        }
        .message.assistant {
            background-color: #f5f5f5;
            margin-right: 2rem;
        }
        .message.system {
            background-color: #fff3cd;
            font-style: italic;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .api-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fff;
        }
        .method-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .method-get { background-color: #28a745; }
        .method-post { background-color: #007bff; }
        .method-put { background-color: #ffc107; color: #000; }
        .method-delete { background-color: #dc3545; }
        .method-patch { background-color: #6f42c1; }
        
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            overflow-x: auto;
        }
        
        .nav-link.active {
            background-color: #007bff !important;
            color: white !important;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="text-center mb-4">
                        <i class="fas fa-robot"></i> RestAgent
                    </h5>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/">
                                <i class="fas fa-comments"></i> 智能对话
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/search' %}active{% endif %}" href="/search">
                                <i class="fas fa-search"></i> API搜索
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/apps/' in request.url.path %}active{% endif %}" href="/apps/store">
                                <i class="fas fa-store"></i> 个性化APP
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/admin' %}active{% endif %}" href="/admin">
                                <i class="fas fa-cog"></i> 系统管理
                            </a>
                        </li>
                    </ul>
                    
                    <hr>
                    
                    <div class="mt-4">
                        <h6 class="text-muted">系统状态</h6>
                        <div id="system-status">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <small class="text-muted">检查中...</small>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Prism.js for code highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <!-- Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('/api/chat/status');
                const data = await response.json();
                
                const statusElement = document.getElementById('system-status');
                if (data.status === 'ready') {
                    statusElement.innerHTML = `
                        <i class="fas fa-circle text-success"></i>
                        <small class="text-success">系统正常</small>
                        <br>
                        <small class="text-muted">API数量: ${data.database.api_count || 0}</small>
                    `;
                } else {
                    statusElement.innerHTML = `
                        <i class="fas fa-circle text-danger"></i>
                        <small class="text-danger">系统异常</small>
                    `;
                }
            } catch (error) {
                const statusElement = document.getElementById('system-status');
                statusElement.innerHTML = `
                    <i class="fas fa-circle text-warning"></i>
                    <small class="text-warning">连接失败</small>
                `;
            }
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', checkSystemStatus);
        
        // 每30秒检查一次状态
        //setInterval(checkSystemStatus, 30000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
