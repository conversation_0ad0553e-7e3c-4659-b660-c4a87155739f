{% extends "base.html" %}

{% block title %}系统管理 - RestAgent{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog"></i> 系统管理
    </h1>
</div>

<!-- 导航标签 -->
<ul class="nav nav-tabs" id="adminTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="sync-tab" data-bs-toggle="tab" data-bs-target="#sync" type="button" role="tab">
            <i class="fas fa-sync"></i> 数据同步
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab">
            <i class="fas fa-cog"></i> 系统配置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab">
            <i class="fas fa-chart-bar"></i> 统计信息
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
            <i class="fas fa-file-alt"></i> 系统日志
        </button>
    </li>
</ul>

<div class="tab-content" id="adminTabContent">
    <!-- 数据同步 -->
    <div class="tab-pane fade show active" id="sync" role="tabpanel">
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> 向量数据库管理</h5>
                    </div>
                    <div class="card-body">
                        <div id="db-info">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <span class="ms-2">正在获取数据库信息...</span>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary me-2" onclick="syncAPIs()">
                                <i class="fas fa-sync"></i> 同步API数据
                            </button>
                            <button class="btn btn-warning me-2" onclick="clearDatabase()">
                                <i class="fas fa-trash"></i> 清空数据库
                            </button>
                            <button class="btn btn-info" onclick="refreshDatabaseInfo()">
                                <i class="fas fa-refresh"></i> 刷新信息
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-project-diagram"></i> 项目信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="project-info">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <span class="ms-2">正在获取项目信息...</span>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-primary" onclick="refreshProjectInfo()">
                                <i class="fas fa-refresh"></i> 刷新项目信息
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 同步日志 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> 操作日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="sync-logs" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 1rem; border-radius: 0.375rem;">
                            <div class="text-muted">暂无操作日志</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统配置 -->
    <div class="tab-pane fade" id="config" role="tabpanel">
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> 配置管理</h5>
                    </div>
                    <div class="card-body">
                        <div id="config-content">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <span class="ms-2">正在加载配置信息...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 连接测试 -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-plug"></i> YApi连接测试</h6>
                    </div>
                    <div class="card-body">
                        <div id="yapi-test-result">
                            <button class="btn btn-outline-primary btn-sm" onclick="testYApiConnection()">
                                <i class="fas fa-play"></i> 测试连接
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-brain"></i> 大模型连接测试</h6>
                    </div>
                    <div class="card-body">
                        <div id="llm-test-result">
                            <button class="btn btn-outline-primary btn-sm" onclick="testLLMConnection()">
                                <i class="fas fa-play"></i> 测试连接
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-database"></i> 向量数据库测试</h6>
                    </div>
                    <div class="card-body">
                        <div id="vectordb-test-result">
                            <button class="btn btn-outline-primary btn-sm" onclick="testVectorDBConnection()">
                                <i class="fas fa-play"></i> 测试连接
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="tab-pane fade" id="stats" role="tabpanel">
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie"></i> API统计</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="apiStatsChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-server"></i> 系统状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="system-status-detail">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <span class="ms-2">正在获取系统状态...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统日志 -->
    <div class="tab-pane fade" id="logs" role="tabpanel">
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-file-alt"></i> 系统日志</h5>
                            <div>
                                <select class="form-select form-select-sm me-2" id="log-level" style="width: auto; display: inline-block;">
                                    <option value="">所有级别</option>
                                    <option value="ERROR">错误</option>
                                    <option value="WARNING">警告</option>
                                    <option value="INFO">信息</option>
                                    <option value="DEBUG">调试</option>
                                </select>
                                <button class="btn btn-outline-primary btn-sm" onclick="loadLogs()">
                                    <i class="fas fa-refresh"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="system-logs" style="height: 500px; overflow-y: auto; background-color: #000; color: #00ff00; padding: 1rem; border-radius: 0.375rem; font-family: 'Courier New', monospace; font-size: 0.875rem;">
                            <div class="text-center text-muted">
                                <i class="fas fa-file-alt fa-2x mb-2"></i>
                                <p>点击刷新按钮加载日志</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let apiStatsChart = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    refreshDatabaseInfo();
    refreshProjectInfo();
    loadConfig();
    loadSystemStatus();
});

// 数据库管理
async function refreshDatabaseInfo() {
    const container = document.getElementById('db-info');
    container.innerHTML = `
        <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <span class="ms-2">正在获取数据库信息...</span>
    `;

    try {
        const response = await fetch('/api/admin/database/info');
        const data = await response.json();

        if (data.success) {
            const dbInfo = data.database_info;
            container.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <strong>状态:</strong>
                        <span class="badge ${dbInfo.status === 'connected' ? 'bg-success' : 'bg-danger'}">
                            ${dbInfo.status === 'connected' ? '已连接' : '未连接'}
                        </span>
                    </div>
                    <div class="col-6">
                        <strong>API数量:</strong> ${dbInfo.api_count || 0}
                    </div>
                    <div class="col-12 mt-2">
                        <strong>集合名称:</strong> ${dbInfo.collection_name || 'N/A'}
                    </div>
                    <div class="col-12">
                        <strong>存储路径:</strong> <small>${dbInfo.persist_directory || 'N/A'}</small>
                    </div>
                </div>
            `;
        } else {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    获取数据库信息失败
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                连接失败: ${error.message}
            </div>
        `;
    }
}

async function syncAPIs() {
    addSyncLog('开始同步API数据...');

    try {
        const response = await fetch('/api/admin/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });

        const data = await response.json();

        if (data.success) {
            addSyncLog('✅ API数据同步成功');
            refreshDatabaseInfo();
        } else {
            addSyncLog(`❌ API数据同步失败: ${data.message}`);
        }
    } catch (error) {
        addSyncLog(`❌ 同步失败: ${error.message}`);
    }
}

async function clearDatabase() {
    if (!confirm('确定要清空向量数据库吗？此操作不可恢复！')) {
        return;
    }

    addSyncLog('开始清空数据库...');

    try {
        const response = await fetch('/api/admin/database/clear', {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            addSyncLog('✅ 数据库清空成功');
            refreshDatabaseInfo();
        } else {
            addSyncLog(`❌ 数据库清空失败: ${data.message}`);
        }
    } catch (error) {
        addSyncLog(`❌ 清空失败: ${error.message}`);
    }
}

function addSyncLog(message) {
    const container = document.getElementById('sync-logs');
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
    container.appendChild(logEntry);
    container.scrollTop = container.scrollHeight;
}

// 项目信息
async function refreshProjectInfo() {
    const container = document.getElementById('project-info');
    container.innerHTML = `
        <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <span class="ms-2">正在获取项目信息...</span>
    `;

    try {
        const response = await fetch('/api/admin/project/info');
        const data = await response.json();

        if (data.success) {
            const project = data.project;
            container.innerHTML = `
                <div class="row">
                    <div class="col-12">
                        <strong>项目名称:</strong> ${project.name || '未知'}
                    </div>
                    <div class="col-12 mt-2">
                        <strong>项目ID:</strong> ${project._id || 'N/A'}
                    </div>
                    <div class="col-12 mt-2">
                        <strong>描述:</strong> ${project.desc || '暂无描述'}
                    </div>
                    <div class="col-12 mt-2">
                        <strong>创建时间:</strong> ${project.add_time ? new Date(project.add_time * 1000).toLocaleString() : 'N/A'}
                    </div>
                </div>
            `;
        } else {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${data.message || '获取项目信息失败'}
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                连接失败: ${error.message}
            </div>
        `;
    }
}

// 配置管理
async function loadConfig() {
    const container = document.getElementById('config-content');

    try {
        const response = await fetch('/api/config/');
        const data = await response.json();

        if (data.success) {
            displayConfig(data.config);
        } else {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    加载配置失败
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                连接失败: ${error.message}
            </div>
        `;
    }
}

function displayConfig(config) {
    const container = document.getElementById('config-content');

    let html = `
        <div class="accordion" id="configAccordion">
    `;

    // YApi配置
    html += `
        <div class="accordion-item">
            <h2 class="accordion-header" id="yapiHeading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#yapiCollapse">
                    <i class="fas fa-api me-2"></i> YApi配置
                </button>
            </h2>
            <div id="yapiCollapse" class="accordion-collapse collapse show" data-bs-parent="#configAccordion">
                <div class="accordion-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>服务地址:</strong> ${config.yapi.base_url}
                        </div>
                        <div class="col-md-6">
                            <strong>用户名:</strong> ${config.yapi.username}
                        </div>
                        <div class="col-md-6">
                            <strong>项目ID:</strong> ${config.yapi.project_id}
                        </div>
                        <div class="col-md-6">
                            <strong>密码:</strong> ${config.yapi.password}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 大模型配置
    html += `
        <div class="accordion-item">
            <h2 class="accordion-header" id="llmHeading">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#llmCollapse">
                    <i class="fas fa-brain me-2"></i> 大模型配置
                </button>
            </h2>
            <div id="llmCollapse" class="accordion-collapse collapse" data-bs-parent="#configAccordion">
                <div class="accordion-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>提供商:</strong> ${config.llm.provider}
                        </div>
                        <div class="col-md-6">
                            <strong>模型名称:</strong> ${config.llm.model_name}
                        </div>
                        <div class="col-md-6">
                            <strong>服务地址:</strong> ${config.llm.base_url}
                        </div>
                        <div class="col-md-6">
                            <strong>API密钥:</strong> ${config.llm.api_key}
                        </div>
                        <div class="col-md-6">
                            <strong>温度:</strong> ${config.llm.temperature}
                        </div>
                        <div class="col-md-6">
                            <strong>最大令牌:</strong> ${config.llm.max_tokens}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    html += `</div>`;

    container.innerHTML = html;
}

// 连接测试
async function testYApiConnection() {
    const container = document.getElementById('yapi-test-result');
    container.innerHTML = `
        <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">测试中...</span>
        </div>
        <span class="ms-2">测试中...</span>
    `;

    try {
        const response = await fetch('/api/config/test/yapi', {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            container.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    连接成功
                </div>
                <small>项目: ${data.project ? data.project.name : 'N/A'}</small>
            `;
        } else {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    ${data.message}
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-times-circle"></i>
                测试失败: ${error.message}
            </div>
        `;
    }
}

async function testLLMConnection() {
    const container = document.getElementById('llm-test-result');
    container.innerHTML = `
        <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">测试中...</span>
        </div>
        <span class="ms-2">测试中...</span>
    `;

    try {
        const response = await fetch('/api/config/test/llm', {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            container.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    连接成功
                </div>
                <small>模型: ${data.model || 'N/A'}</small>
            `;
        } else {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    ${data.message}
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-times-circle"></i>
                测试失败: ${error.message}
            </div>
        `;
    }
}

async function testVectorDBConnection() {
    const container = document.getElementById('vectordb-test-result');
    container.innerHTML = `
        <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">测试中...</span>
        </div>
        <span class="ms-2">测试中...</span>
    `;

    try {
        const response = await fetch('/api/config/test/vector_db', {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            container.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    连接成功
                </div>
                <small>API数量: ${data.info ? data.info.api_count : 0}</small>
            `;
        } else {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    ${data.message}
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-times-circle"></i>
                测试失败: ${error.message}
            </div>
        `;
    }
}

// 系统状态
async function loadSystemStatus() {
    const container = document.getElementById('system-status-detail');

    try {
        const response = await fetch('/api/admin/system/status');
        const data = await response.json();

        if (data.success) {
            const system = data.system;
            container.innerHTML = `
                <div class="row">
                    <div class="col-12">
                        <strong>平台:</strong> ${system.platform || 'Unknown'}
                    </div>
                    <div class="col-12 mt-2">
                        <strong>Python版本:</strong> ${system.python_version || 'N/A'}
                    </div>
                    <div class="col-12 mt-2">
                        <strong>CPU核心:</strong> ${system.cpu_count || 'N/A'}
                    </div>
                    <div class="col-12 mt-2">
                        <strong>内存使用:</strong> ${system.memory_available && system.memory_total ?
                            `${((system.memory_total - system.memory_available) / system.memory_total * 100).toFixed(1)}%` : 'N/A'}
                    </div>
                </div>
            `;

            // 加载API统计图表
            loadAPIStats();
        } else {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    获取系统状态失败
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                连接失败: ${error.message}
            </div>
        `;
    }
}

async function loadAPIStats() {
    try {
        const response = await fetch('/api/admin/project/statistics');
        const data = await response.json();

        if (data.success && data.statistics) {
            const stats = data.statistics;

            // 创建图表
            const ctx = document.getElementById('apiStatsChart').getContext('2d');

            if (apiStatsChart) {
                apiStatsChart.destroy();
            }

            apiStatsChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(stats.methods || {}),
                    datasets: [{
                        data: Object.values(stats.methods || {}),
                        backgroundColor: [
                            '#28a745', // GET
                            '#007bff', // POST
                            '#ffc107', // PUT
                            '#dc3545', // DELETE
                            '#6f42c1'  // PATCH
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        title: {
                            display: true,
                            text: `API方法分布 (总计: ${stats.total_apis || 0})`
                        }
                    }
                }
            });
        }
    } catch (error) {
        console.error('加载API统计失败:', error);
    }
}

// 日志管理
async function loadLogs() {
    const container = document.getElementById('system-logs');
    const level = document.getElementById('log-level').value;

    container.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-success">正在加载日志...</p>
        </div>
    `;

    try {
        let url = '/api/admin/logs?lines=200';
        if (level) {
            url += `&level=${level}`;
        }

        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
            const logs = data.logs;
            if (logs.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <p>没有找到日志记录</p>
                    </div>
                `;
            } else {
                let logHtml = '';
                logs.forEach(log => {
                    // 简单的日志着色
                    let color = '#00ff00'; // 默认绿色
                    if (log.includes('ERROR')) color = '#ff0000';
                    else if (log.includes('WARNING')) color = '#ffff00';
                    else if (log.includes('INFO')) color = '#00ffff';

                    logHtml += `<div style="color: ${color}; margin-bottom: 2px;">${log.trim()}</div>`;
                });
                container.innerHTML = logHtml;
                container.scrollTop = container.scrollHeight;
            }
        } else {
            container.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>加载日志失败: ${data.message || '未知错误'}</p>
                </div>
            `;
        }
    } catch (error) {
        container.innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>加载日志失败: ${error.message}</p>
            </div>
        `;
    }
}
</script>
{% endblock %}
