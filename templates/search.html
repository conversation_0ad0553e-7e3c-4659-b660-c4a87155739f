{% extends "base.html" %}

{% block title %}API搜索 - RestAgent{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-search"></i> API搜索
    </h1>
</div>

<!-- 搜索区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">搜索API接口</h5>
                
                <!-- 搜索表单 -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search-input" placeholder="输入关键词搜索API..." onkeypress="handleSearchKeyPress(event)">
                            <button class="btn btn-primary" type="button" onclick="searchAPIs()">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="search-type">
                            <option value="vector">向量搜索</option>
                            <option value="keyword">关键词搜索</option>
                        </select>
                    </div>
                </div>
                
                <!-- 快捷搜索 -->
                <div class="mt-3">
                    <h6>快捷搜索：</h6>
                    <div class="btn-group flex-wrap" role="group">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="quickSearch('用户')">用户</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="quickSearch('登录')">登录</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="quickSearch('订单')">订单</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="quickSearch('支付')">支付</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="quickSearch('商品')">商品</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索结果 -->
<div class="row">
    <div class="col-12">
        <div id="search-results">
            <div class="text-center text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>输入关键词开始搜索API接口</p>
            </div>
        </div>
    </div>
</div>

<!-- API详情模态框 -->
<div class="modal fade" id="apiDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">API详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="api-detail-content">
                <!-- API详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="testAPI()">测试调用</button>
            </div>
        </div>
    </div>
</div>

<!-- API测试模态框 -->
<div class="modal fade" id="apiTestModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">API测试</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="api-test-content">
                <!-- API测试内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" onclick="callAPI()">发送请求</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentAPI = null;
let searchResults = [];

function handleSearchKeyPress(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        searchAPIs();
    }
}

function quickSearch(keyword) {
    document.getElementById('search-input').value = keyword;
    searchAPIs();
}

async function searchAPIs() {
    const query = document.getElementById('search-input').value.trim();
    const searchType = document.getElementById('search-type').value;
    
    if (!query) {
        alert('请输入搜索关键词');
        return;
    }
    
    const resultsContainer = document.getElementById('search-results');
    resultsContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">搜索中...</span>
            </div>
            <p class="mt-2">正在搜索API接口...</p>
        </div>
    `;
    
    try {
        let url, params;
        
        if (searchType === 'vector') {
            url = '/api/search/vector';
            params = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query,
                    n_results: 20
                })
            };
        } else {
            url = `/api/search/keyword?q=${encodeURIComponent(query)}`;
            params = {
                method: 'GET'
            };
        }
        
        const response = await fetch(url, params);
        const data = await response.json();
        
        if (data.success) {
            searchResults = data.results;
            displaySearchResults(data.results, query);
        } else {
            resultsContainer.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    搜索失败：${data.message || '未知错误'}
                </div>
            `;
        }
        
    } catch (error) {
        console.error('搜索失败:', error);
        resultsContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                搜索失败：${error.message}
            </div>
        `;
    }
}

function displaySearchResults(results, query) {
    const container = document.getElementById('search-results');
    
    if (results.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                没有找到与 "${query}" 相关的API接口
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>搜索结果 (${results.length} 个)</h5>
            <small class="text-muted">搜索关键词: "${query}"</small>
        </div>
    `;
    
    results.forEach((api, index) => {
        const methodClass = `method-${api.method ? api.method.toLowerCase() : 'get'}`;
        const similarity = api.similarity_score ? (api.similarity_score * 100).toFixed(1) : 'N/A';
        
        html += `
            <div class="api-card">
                <div class="row">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge ${methodClass} method-badge me-2">${api.method || 'GET'}</span>
                            <h6 class="mb-0">${api.title || '未命名接口'}</h6>
                        </div>
                        <p class="text-muted mb-2">${api.path || ''}</p>
                        <p class="mb-2">${api.document ? api.document.substring(0, 200) + '...' : '暂无描述'}</p>
                        <small class="text-muted">
                            分类: ${api.category || '未分类'} | 
                            状态: ${api.status || 'unknown'}
                            ${similarity !== 'N/A' ? ` | 相似度: ${similarity}%` : ''}
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="viewAPIDetail('${api.api_id}')">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="testAPIQuick('${api.api_id}')">
                            <i class="fas fa-play"></i> 快速测试
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

async function viewAPIDetail(apiId) {
    try {
        const response = await fetch(`/api/search/detail/${apiId}`);
        const data = await response.json();
        
        if (data.success) {
            currentAPI = data.api;
            displayAPIDetail(data.api);
            
            const modal = new bootstrap.Modal(document.getElementById('apiDetailModal'));
            modal.show();
        } else {
            alert('获取API详情失败');
        }
    } catch (error) {
        console.error('获取API详情失败:', error);
        alert('获取API详情失败');
    }
}

function displayAPIDetail(api) {
    const content = document.getElementById('api-detail-content');
    
    let html = `
        <div class="row">
            <div class="col-12">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>接口名称:</strong></td><td>${api.title || '未命名'}</td></tr>
                    <tr><td><strong>请求方式:</strong></td><td><span class="badge method-${api.method.toLowerCase()}">${api.method}</span></td></tr>
                    <tr><td><strong>请求路径:</strong></td><td><code>${api.path}</code></td></tr>
                    <tr><td><strong>分类:</strong></td><td>${api.catname || '未分类'}</td></tr>
                    <tr><td><strong>状态:</strong></td><td>${api.status}</td></tr>
                    <tr><td><strong>描述:</strong></td><td>${api.desc || '暂无描述'}</td></tr>
                </table>
            </div>
        </div>
    `;
    
    // 请求参数
    if (api.req_query && api.req_query.length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>查询参数</h6>
                    <table class="table table-sm">
                        <thead>
                            <tr><th>参数名</th><th>描述</th><th>必需</th><th>示例</th></tr>
                        </thead>
                        <tbody>
        `;
        
        api.req_query.forEach(param => {
            html += `
                <tr>
                    <td><code>${param.name}</code></td>
                    <td>${param.desc || ''}</td>
                    <td>${param.required ? '是' : '否'}</td>
                    <td>${param.example || ''}</td>
                </tr>
            `;
        });
        
        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }
    
    // 请求体
    if (api.req_body_other) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>请求体示例</h6>
                    <pre><code class="language-json">${api.req_body_other}</code></pre>
                </div>
            </div>
        `;
    }
    
    // 响应示例
    if (api.res_body) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>响应示例</h6>
                    <pre><code class="language-json">${api.res_body}</code></pre>
                </div>
            </div>
        `;
    }
    
    content.innerHTML = html;
    
    // 应用语法高亮
    if (window.Prism) {
        Prism.highlightAllUnder(content);
    }
}

function testAPI() {
    if (!currentAPI) return;
    
    // 关闭详情模态框
    const detailModal = bootstrap.Modal.getInstance(document.getElementById('apiDetailModal'));
    detailModal.hide();
    
    // 显示测试模态框
    displayAPITest(currentAPI);
    const testModal = new bootstrap.Modal(document.getElementById('apiTestModal'));
    testModal.show();
}

function testAPIQuick(apiId) {
    // 先获取API详情，然后显示测试界面
    viewAPIDetail(apiId).then(() => {
        setTimeout(() => testAPI(), 500);
    });
}

function displayAPITest(api) {
    const content = document.getElementById('api-test-content');
    
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>请求配置</h6>
                <form id="api-test-form">
                    <div class="mb-3">
                        <label class="form-label">基础URL (可选)</label>
                        <input type="text" class="form-control" id="base-url" placeholder="http://localhost:8080">
                    </div>
    `;
    
    // 查询参数
    if (api.req_query && api.req_query.length > 0) {
        html += '<h6>查询参数</h6>';
        api.req_query.forEach(param => {
            html += `
                <div class="mb-3">
                    <label class="form-label">${param.name} ${param.required ? '<span class="text-danger">*</span>' : ''}</label>
                    <input type="text" class="form-control" name="query_${param.name}" placeholder="${param.desc || param.example || ''}">
                </div>
            `;
        });
    }
    
    // 路径参数
    if (api.req_params && api.req_params.length > 0) {
        html += '<h6>路径参数</h6>';
        api.req_params.forEach(param => {
            html += `
                <div class="mb-3">
                    <label class="form-label">${param.name} <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="path_${param.name}" placeholder="${param.desc || param.example || ''}">
                </div>
            `;
        });
    }
    
    // 请求体
    if (api.req_body_type === 'json' && api.req_body_other) {
        html += `
            <div class="mb-3">
                <label class="form-label">请求体 (JSON)</label>
                <textarea class="form-control" id="request-body" rows="8" placeholder="请输入JSON格式的请求体">${api.req_body_other}</textarea>
            </div>
        `;
    }
    
    html += `
                </form>
            </div>
            <div class="col-md-6">
                <h6>响应结果</h6>
                <div id="api-response" style="height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem; background-color: #f8f9fa;">
                    <div class="text-muted text-center">
                        <i class="fas fa-play-circle fa-2x mb-2"></i>
                        <p>点击"发送请求"查看响应结果</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    content.innerHTML = html;
}

async function callAPI() {
    if (!currentAPI) return;
    
    const responseDiv = document.getElementById('api-response');
    responseDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">请求中...</span>
            </div>
            <p class="mt-2">正在发送请求...</p>
        </div>
    `;
    
    try {
        // 收集参数
        const form = document.getElementById('api-test-form');
        const formData = new FormData(form);
        const parameters = {};
        
        // 处理表单数据
        for (let [key, value] of formData.entries()) {
            if (value.trim()) {
                // 移除前缀
                const cleanKey = key.replace(/^(query_|path_)/, '');
                parameters[cleanKey] = value;
            }
        }
        
        // 处理请求体
        const requestBodyElement = document.getElementById('request-body');
        if (requestBodyElement && requestBodyElement.value.trim()) {
            parameters.request_body = requestBodyElement.value;
        }
        
        const baseUrl = document.getElementById('base-url').value.trim();
        
        const response = await fetch('/api/search/call', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                api_id: currentAPI._id,
                parameters: parameters,
                base_url: baseUrl || null
            })
        });
        
        const data = await response.json();
        
        let resultHtml = '';
        
        if (data.success) {
            const result = data.result;
            resultHtml = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> 请求成功
                </div>
                <h6>状态码: ${result.status_code}</h6>
                <h6>响应头:</h6>
                <pre><code class="language-json">${JSON.stringify(result.headers, null, 2)}</code></pre>
                <h6>响应体:</h6>
                <pre><code class="language-json">${JSON.stringify(result.response, null, 2)}</code></pre>
            `;
        } else {
            resultHtml = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 请求失败
                </div>
                <p><strong>错误信息:</strong> ${data.result ? data.result.error : '未知错误'}</p>
            `;
        }
        
        responseDiv.innerHTML = resultHtml;
        
        // 应用语法高亮
        if (window.Prism) {
            Prism.highlightAllUnder(responseDiv);
        }
        
    } catch (error) {
        console.error('API调用失败:', error);
        responseDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> 请求失败: ${error.message}
            </div>
        `;
    }
}
</script>
{% endblock %}
