{% extends "base.html" %}

{% block title %}个性化APP商城 - RestAgent{% endblock %}

{% block extra_css %}
<style>
    .app-card {
        transition: transform 0.2s, box-shadow 0.2s;
        height: 100%;
    }
    .app-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .app-icon {
        font-size: 3rem;
        color: #007bff;
    }
    .app-stats {
        font-size: 0.875rem;
        color: #6c757d;
    }
    .category-filter {
        cursor: pointer;
        transition: all 0.2s;
    }
    .category-filter:hover {
        background-color: #f8f9fa;
    }
    .category-filter.active {
        background-color: #007bff;
        color: white;
    }
    .search-box {
        border-radius: 25px;
    }
    .create-app-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        z-index: 1000;
    }
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    .loading-spinner {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-store me-2"></i>个性化APP商城
            </h1>
            <p class="text-muted mb-0">创建和管理您的个性化应用</p>
        </div>
        <button class="btn btn-primary" onclick="createNewApp()">
            <i class="fas fa-plus me-2"></i>创建APP
        </button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control search-box" id="searchInput" 
                       placeholder="搜索APP名称、描述或标签...">
            </div>
        </div>
        <div class="col-md-6">
            <div class="d-flex gap-2 flex-wrap">
                <span class="badge category-filter active" data-category="">
                    全部
                </span>
                <span class="badge category-filter" data-category="工具">
                    工具
                </span>
                <span class="badge category-filter" data-category="娱乐">
                    娱乐
                </span>
                <span class="badge category-filter" data-category="商务">
                    商务
                </span>
                <span class="badge category-filter" data-category="教育">
                    教育
                </span>
                <span class="badge category-filter" data-category="其他">
                    其他
                </span>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-spinner text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2">正在加载APP列表...</p>
    </div>

    <!-- APP列表 -->
    <div id="appList" class="row">
        <!-- APP卡片将通过JavaScript动态加载 -->
    </div>

    <!-- 空状态 -->
    <div id="emptyState" class="empty-state" style="display: none;">
        <i class="fas fa-cube fa-4x mb-3"></i>
        <h4>还没有APP</h4>
        <p>点击右下角的"+"按钮创建您的第一个个性化APP</p>
        <button class="btn btn-primary" onclick="createNewApp()">
            <i class="fas fa-plus me-2"></i>立即创建
        </button>
    </div>
</div>

<!-- 创建APP按钮 -->
<button class="btn btn-primary create-app-btn" onclick="createNewApp()" title="创建新APP">
    <i class="fas fa-plus"></i>
</button>

<!-- APP详情模态框 -->
<div class="modal fade" id="appDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">APP详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="appDetailContent">
                    <!-- APP详情内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="editAppBtn">编辑APP</button>
                <button type="button" class="btn btn-success" id="viewAppBtn">查看APP</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentApps = [];
let currentCategory = '';
let currentSearch = '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadApps();
    
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        currentSearch = this.value.trim();
        filterApps();
    });
    
    // 分类筛选
    document.querySelectorAll('.category-filter').forEach(filter => {
        filter.addEventListener('click', function() {
            // 更新激活状态
            document.querySelectorAll('.category-filter').forEach(f => f.classList.remove('active'));
            this.classList.add('active');
            
            currentCategory = this.dataset.category;
            filterApps();
        });
    });
});

// 加载APP列表
async function loadApps() {
    try {
        showLoading(true);
        
        const response = await fetch('/apps/api/apps');
        const data = await response.json();
        
        if (data.success) {
            currentApps = data.apps;
            renderApps(currentApps);
        } else {
            showError('加载APP列表失败');
        }
    } catch (error) {
        console.error('加载APP列表失败:', error);
        showError('加载APP列表失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 筛选APP
function filterApps() {
    let filteredApps = currentApps;
    
    // 分类筛选
    if (currentCategory) {
        filteredApps = filteredApps.filter(app => app.category === currentCategory);
    }
    
    // 搜索筛选
    if (currentSearch) {
        const searchLower = currentSearch.toLowerCase();
        filteredApps = filteredApps.filter(app => 
            app.name.toLowerCase().includes(searchLower) ||
            app.description.toLowerCase().includes(searchLower) ||
            (app.tags && app.tags.some(tag => tag.toLowerCase().includes(searchLower)))
        );
    }
    
    renderApps(filteredApps);
}

// 渲染APP列表
function renderApps(apps) {
    const appList = document.getElementById('appList');
    const emptyState = document.getElementById('emptyState');
    
    if (apps.length === 0) {
        appList.innerHTML = '';
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    
    appList.innerHTML = apps.map(app => `
        <div class="col-md-6 col-lg-4 col-xl-3 mb-4">
            <div class="card app-card h-100">
                <div class="card-body d-flex flex-column">
                    <div class="text-center mb-3">
                        <i class="${app.icon || 'fas fa-cube'} app-icon"></i>
                    </div>
                    <h5 class="card-title text-center">${app.name}</h5>
                    <p class="card-text text-muted small flex-grow-1">${app.description}</p>
                    
                    <div class="app-stats mb-3">
                        <div class="d-flex justify-content-between">
                            <span><i class="fas fa-eye me-1"></i>${app.access_count || 0} 次访问</span>
                            <span><i class="fas fa-clock me-1"></i>${formatDate(app.updated_at)}</span>
                        </div>
                        <div class="mt-1">
                            <span class="badge bg-secondary">${app.category || '其他'}</span>
                            ${(app.tags || []).map(tag => `<span class="badge bg-light text-dark">${tag}</span>`).join(' ')}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" onclick="viewApp('${app.id}')">
                            <i class="fas fa-eye me-1"></i>查看
                        </button>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="editApp('${app.id}')">
                                <i class="fas fa-edit me-1"></i>编辑
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="showAppDetail('${app.id}')">
                                <i class="fas fa-info me-1"></i>详情
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteApp('${app.id}', '${app.name}')">
                                <i class="fas fa-trash me-1"></i>删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// 显示/隐藏加载状态
function showLoading(show) {
    const spinner = document.querySelector('.loading-spinner');
    const appList = document.getElementById('appList');
    
    if (show) {
        spinner.style.display = 'block';
        appList.style.display = 'none';
    } else {
        spinner.style.display = 'none';
        appList.style.display = 'flex';
    }
}

// 显示错误信息
function showError(message) {
    // 这里可以使用更好的错误提示组件
    alert('错误: ' + message);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 创建新APP
function createNewApp() {
    window.location.href = '/apps/builder';
}

// 查看APP
function viewApp(appId) {
    window.open(`/apps/view/${appId}`, '_blank');
}

// 编辑APP
function editApp(appId) {
    window.location.href = `/apps/builder?app_id=${appId}`;
}

// 显示APP详情
async function showAppDetail(appId) {
    try {
        const response = await fetch(`/apps/api/apps/${appId}`);
        const data = await response.json();
        
        if (data.success) {
            const app = data.app;
            document.getElementById('appDetailContent').innerHTML = `
                <div class="row">
                    <div class="col-md-4 text-center">
                        <i class="${app.icon || 'fas fa-cube'}" style="font-size: 4rem; color: #007bff;"></i>
                        <h4 class="mt-2">${app.name}</h4>
                        <p class="text-muted">${app.description}</p>
                    </div>
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr><td><strong>分类:</strong></td><td>${app.category || '其他'}</td></tr>
                            <tr><td><strong>作者:</strong></td><td>${app.author || '用户'}</td></tr>
                            <tr><td><strong>版本:</strong></td><td>${app.version || '1.0.0'}</td></tr>
                            <tr><td><strong>创建时间:</strong></td><td>${formatDate(app.created_at)}</td></tr>
                            <tr><td><strong>更新时间:</strong></td><td>${formatDate(app.updated_at)}</td></tr>
                            <tr><td><strong>访问次数:</strong></td><td>${app.access_count || 0}</td></tr>
                            <tr><td><strong>页面数量:</strong></td><td>${(app.pages || []).length}</td></tr>
                        </table>
                        ${(app.tags && app.tags.length > 0) ? `
                            <div class="mt-3">
                                <strong>标签:</strong><br>
                                ${app.tags.map(tag => `<span class="badge bg-light text-dark me-1">${tag}</span>`).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            
            // 设置按钮事件
            document.getElementById('editAppBtn').onclick = () => editApp(appId);
            document.getElementById('viewAppBtn').onclick = () => viewApp(appId);
            
            // 显示模态框
            new bootstrap.Modal(document.getElementById('appDetailModal')).show();
        } else {
            showError('获取APP详情失败');
        }
    } catch (error) {
        console.error('获取APP详情失败:', error);
        showError('获取APP详情失败: ' + error.message);
    }
}

// 删除APP
async function deleteApp(appId, appName) {
    if (!confirm(`确定要删除APP "${appName}" 吗？此操作不可恢复。`)) {
        return;
    }
    
    try {
        const response = await fetch(`/apps/api/apps/${appId}`, {
            method: 'DELETE'
        });
        const data = await response.json();
        
        if (data.success) {
            // 重新加载APP列表
            loadApps();
            alert('APP删除成功');
        } else {
            showError('删除APP失败');
        }
    } catch (error) {
        console.error('删除APP失败:', error);
        showError('删除APP失败: ' + error.message);
    }
}
</script>
{% endblock %}
