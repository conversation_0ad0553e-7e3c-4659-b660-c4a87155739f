{% extends "base.html" %}

{% block title %}智能对话 - RestAgent{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-comments"></i> 智能对话
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearChat()">
            <i class="fas fa-trash"></i> 清空对话
        </button>
    </div>
</div>

<!-- 对话区域 -->
<div class="row">
    <div class="col-12">
        <div class="chat-container" id="chat-container">
            <div class="message system">
                <i class="fas fa-robot"></i>
                <strong>RestAgent:</strong> 您好！我是REST API智能助手，可以帮您调用各种API接口。请告诉我您需要什么帮助？
            </div>
        </div>
        
        <!-- 输入区域 -->
        <div class="mt-3">
            <div class="input-group">
                <input type="text" class="form-control" id="user-input" placeholder="请输入您的问题..." onkeypress="handleKeyPress(event)">
                <button class="btn btn-primary" type="button" onclick="sendMessage()" id="send-btn">
                    <i class="fas fa-paper-plane"></i> 发送
                </button>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="mt-3">
            <h6>快捷操作：</h6>
            <div class="btn-group-vertical btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-info" onclick="quickSend('帮我搜索用户相关的API')">
                    搜索用户API
                </button>
                <button type="button" class="btn btn-outline-info" onclick="quickSend('显示所有可用的API接口')">
                    显示所有API
                </button>
                <button type="button" class="btn btn-outline-info" onclick="quickSend('帮我调用获取用户信息的接口')">
                    获取用户信息
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let isWaiting = false;
let conversationHistory = [];

function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

function quickSend(message) {
    document.getElementById('user-input').value = message;
    sendMessage();
}

async function sendMessage() {
    const input = document.getElementById('user-input');
    const message = input.value.trim();

    if (!message || isWaiting) return;

    // 清空输入框
    input.value = '';

    // 添加用户消息到界面
    addMessage('user', message);

    // 设置等待状态
    isWaiting = true;
    updateSendButton(true);

    try {
        // 发送请求
        const response = await fetch('/api/chat/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                history: conversationHistory,
                max_iterations: 5
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);

                    if (data === '[DONE]') {
                        continue;
                    }

                    try {
                        const stepData = JSON.parse(data);
                        handleChatStep(stepData);
                    } catch (e) {
                        console.error('解析响应数据失败:', e);
                    }
                }
            }
        }

        // 更新对话历史
        conversationHistory.push({role: 'user', content: message});

    } catch (error) {
        console.error('发送消息失败:', error);
        addMessage('error', `发送消息失败: ${error.message}`);
    } finally {
        isWaiting = false;
        updateSendButton(false);
    }
}

function handleChatStep(step) {
    const type = step.type;
    const message = step.message;

    switch (type) {
        case 'search':
            addMessage('system', `🔍 ${message}`);
            break;

        case 'tools_found':
            addMessage('system', `🛠️ ${message}`);
            if (step.tools && step.tools.length > 0) {
                const toolsList = step.tools.map(tool => `• ${tool}`).join('\n');
                addMessage('system', `可用工具:\n${toolsList}`);
            }
            break;

        case 'thinking':
            addMessage('system', `🤔 ${message}`);
            break;

        case 'tool_call':
            addMessage('system', `⚡ ${message}`);
            break;

        case 'tool_result':
            const result = step.result;
            if (result.success) {
                addMessage('system', `✅ API调用成功`);
                if (result.response) {
                    addMessage('assistant', `API返回结果:\n${JSON.stringify(result.response, null, 2)}`);
                }
            } else {
                addMessage('system', `❌ API调用失败: ${result.error}`);
            }
            break;

        case 'response':
            if (message) {
                addMessage('assistant', message);
                conversationHistory.push({role: 'assistant', content: message});
            }
            break;

        case 'error':
            addMessage('error', `❌ ${message}`);
            break;

        default:
            addMessage('system', message);
    }
}

function addMessage(type, content) {
    const container = document.getElementById('chat-container');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;

    let icon = '';
    let prefix = '';

    switch (type) {
        case 'user':
            icon = '<i class="fas fa-user"></i>';
            prefix = '您';
            break;
        case 'assistant':
            icon = '<i class="fas fa-robot"></i>';
            prefix = 'RestAgent';
            break;
        case 'system':
            icon = '<i class="fas fa-info-circle"></i>';
            prefix = '系统';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-triangle"></i>';
            prefix = '错误';
            break;
    }

    // 处理代码块和JSON格式化
    let formattedContent = content;
    if (content.includes('{') && content.includes('}')) {
        try {
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const jsonStr = jsonMatch[0];
                const jsonObj = JSON.parse(jsonStr);
                const prettyJson = JSON.stringify(jsonObj, null, 2);
                formattedContent = content.replace(jsonStr, `<pre><code class="language-json">${prettyJson}</code></pre>`);
            }
        } catch (e) {
            // 不是有效的JSON，保持原样
        }
    }

    messageDiv.innerHTML = `
        ${icon}
        <strong>${prefix}:</strong>
        <span>${formattedContent.replace(/\n/g, '<br>')}</span>
    `;

    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;

    // 如果有代码块，应用语法高亮
    if (window.Prism) {
        Prism.highlightAllUnder(messageDiv);
    }
}

function updateSendButton(waiting) {
    const btn = document.getElementById('send-btn');
    if (waiting) {
        btn.innerHTML = '<div class="loading"></div> 处理中...';
        btn.disabled = true;
    } else {
        btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发送';
        btn.disabled = false;
    }
}

function clearChat() {
    if (confirm('确定要清空对话记录吗？')) {
        document.getElementById('chat-container').innerHTML = `
            <div class="message system">
                <i class="fas fa-robot"></i>
                <strong>RestAgent:</strong> 您好！我是REST API智能助手，可以帮您调用各种API接口。请告诉我您需要什么帮助？
            </div>
        `;
        conversationHistory = [];
    }
}
</script>
{% endblock %}
