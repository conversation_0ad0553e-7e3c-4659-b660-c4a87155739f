"""
日志配置模块
"""

import os
import sys
from loguru import logger
from .settings import get_settings


def setup_logger():
    """设置日志配置"""
    settings = get_settings()
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 确保日志目录存在
    log_dir = os.path.dirname(settings.logging.file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        level=settings.logging.level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{file}:{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # 添加文件输出
    # logger.add(
    #     settings.logging.file,
    #     level=settings.logging.level,
    #     format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{line} - {message}",
    #     rotation="10 MB",
    #     retention="7 days",
    #     compression="zip"
    # )
    
    return logger


# 初始化日志
setup_logger()