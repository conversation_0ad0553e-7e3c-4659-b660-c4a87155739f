# anthropic_openai_bridge.py
import os, json, uuid, time, traceback
from typing import List, Dict, Any, AsyncItera<PERSON>, Optional, Union
from pydantic import BaseModel, Field
from openai import AsyncOpenAI
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import traceback

class ContentBlock(BaseModel):
    type: str  # text, image, tool_use, tool_result
    text: Optional[str] = None
    source: Optional[Dict] = None  # image
    id: Optional[str] = None       # tool_use
    name: Optional[str] = None     # tool_use
    input: Optional[Dict] = None   # tool_use
    tool_use_id: Optional[str] = None  # tool_result
    content: Optional[Union[str, List[Any]]] = None      # tool_result - 支持字符串或列表
    # 新增字段以支持更多复杂结构
    is_error: Optional[bool] = None
    cache_control: Optional[Dict] = None

class Message(BaseModel):
    role: str  # user / assistant
    content: Union[str, List[ContentBlock]]

class ToolFunction(BaseModel):
    name: str
    description: str
    parameters: Dict[str, Any]

class Tool(BaseModel):
    type: str = "function"
    function: Optional[ToolFunction] = None
    # 支持Claude客户端的直接工具格式
    name: Optional[str] = None
    description: Optional[str] = None
    input_schema: Optional[Dict[str, Any]] = None

class AnthropicRequest(BaseModel):
    model: str
    messages: List[Message]
    max_tokens: int = 8192
    temperature: float = 1.0
    system: Optional[Union[str, List[Dict[str, Any]]]] = None  # 支持字符串或列表格式
    tools: Optional[List[Tool]] = None
    stream: bool = False
    tool_choice: Any = None
def anthropic_to_openai_messages(req: AnthropicRequest) -> List[Dict[str, Any]]:
    openai_msgs = []
    if req.system:
        if isinstance(req.system, str):
            openai_msgs.append({"role": "system", "content": req.system})
        elif isinstance(req.system, list):
            # 处理列表格式的system消息
            system_content = ""
            for item in req.system:
                if isinstance(item, dict) and item.get("type") == "text":
                    system_content += item.get("text", "")
            if system_content:
                openai_msgs.append({"role": "system", "content": system_content})
    
    for m in req.messages:
        if isinstance(m.content, str):
            openai_msgs.append({"role": m.role, "content": m.content})
        else:
            parts = []
            for block in m.content:
                if block.type == "text":
                    parts.append({"type": "text", "text": block.text})
                elif block.type == "image" and block.source:
                    parts.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:{block.source['media_type']};base64,{block.source['data']}"
                        }
                    })
                elif block.type == "tool_result":
                    # 处理tool_result的content字段
                    content_text = ""
                    if isinstance(block.content, str):
                        content_text = block.content
                    elif isinstance(block.content, list):
                        # 如果content是列表，尝试提取文本内容
                        for item in block.content:
                            if isinstance(item, dict):
                                if item.get("type") == "text":
                                    content_text += item.get("text", "")
                                else:
                                    # 对于其他类型，尝试序列化为字符串
                                    content_text += json.dumps(item, ensure_ascii=False)
                            else:
                                content_text += str(item)
                    parts.append({
                        "type": "text",
                        "text": content_text
                    })
                elif block.type == "tool_use":
                    # 处理tool_use类型，转换为OpenAI的tool_calls格式
                    # 注意：OpenAI的tool_calls在message级别，不在content内
                    # 这里暂时跳过，后续在message级别处理
                    pass
            
            # 检查是否有tool_use类型，如果有则需要特殊处理
            tool_calls = []
            for block in m.content:
                if block.type == "tool_use":
                    tool_calls.append({
                        "id": block.id,
                        "type": "function",
                        "function": {
                            "name": block.name,
                            "arguments": json.dumps(block.input or {}, ensure_ascii=False)
                        }
                    })
            
            if tool_calls:
                # 如果有工具调用，创建assistant消息并添加tool_calls
                msg = {"role": m.role, "content": "" if not parts else None}
                if parts:
                    # 如果有文本内容，合并所有文本
                    text_content = ""
                    for part in parts:
                        if part.get("type") == "text":
                            text_content += part.get("text", "")
                    msg["content"] = text_content
                msg["tool_calls"] = tool_calls
                openai_msgs.append(msg)
            elif parts:
                openai_msgs.append({"role": m.role, "content": parts})
            else:
                # 如果没有内容，添加空消息
                openai_msgs.append({"role": m.role, "content": ""})
    return openai_msgs

def anthropic_to_openai_tools(req: AnthropicRequest) -> Optional[List[Dict]]:
    if not req.tools:
        return None
    
    openai_tools = []
    for tool in req.tools:
        if tool.function:
            # 标准格式：具有function字段
            openai_tools.append({
                "type": "function", 
                "function": tool.function.model_dump()
            })
        elif tool.name and tool.description:
            # Claude客户端格式：直接包含name和description
            function_def = {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.input_schema or {}
            }
            openai_tools.append({
                "type": "function",
                "function": function_def
            })
    
    return openai_tools if openai_tools else None

def openai_to_anthropic_response(openai_resp: Dict, model: str) -> Dict[str, Any]:
    msg = openai_resp["choices"][0]["message"]
    content = []
    if msg.get("content"):
        content.append({"type": "text", "text": msg["content"]})
    if msg.get("tool_calls"):
        for tc in msg["tool_calls"]:
            # 安全解析工具调用参数，处理空字符串情况
            arguments_str = tc["function"]["arguments"]
            try:
                if arguments_str and arguments_str.strip():
                    parsed_input = json.loads(arguments_str)
                else:
                    parsed_input = {}
            except json.JSONDecodeError as e:
                print(f"Warning: Failed to parse tool arguments: {arguments_str}, error: {e}")
                # 保持原始字符串，但作为文本而非JSON
                parsed_input = {"raw_arguments": arguments_str}
            
            content.append({
                "type": "tool_use",
                "id": tc["id"],
                "name": tc["function"]["name"],
                "input": parsed_input
            })
    return {
        "id": openai_resp["id"],
        "type": "message",
        "role": "assistant",
        "content": content,
        "model": model,
        "stop_reason": openai_resp["choices"][0]["finish_reason"],
        "usage": {
            "input_tokens": openai_resp["usage"]["prompt_tokens"],
            "output_tokens": openai_resp["usage"]["completion_tokens"]
        }
    }
async def openai_to_anthropic_stream(openai_stream, model: str) -> AsyncIterator[str]:
    """将OpenAI流式响应转换为Anthropic格式"""
    try:
        print("Starting stream conversion...")

        # 发送message_start事件
        message_id = f"msg_{uuid.uuid4().hex}"
        start_event = {
            "type": "message_start",
            "message": {
                "id": message_id,
                "type": "message",
                "role": "assistant",
                "content": [],
                "model": model,
                "stop_reason": None,
                "stop_sequence": None,
                "usage": {"input_tokens": 0, "output_tokens": 0}
            }
        }
        #print(f"Sending message_start: {start_event}")
        yield f"event: message_start\ndata: {json.dumps(start_event)}\n\n"

        # 发送content_block_start事件
        content_start = {
            "type": "content_block_start",
            "index": 0,
            "content_block": {"type": "text", "text": ""}
        }
        #print(f"Sending content_block_start: {content_start}")
        yield f"event: content_block_start\ndata: {json.dumps(content_start)}\n\n"

        # 处理流式数据
        chunk_count = 0
        has_content = False
        tool_call_index = 1
        current_tool_calls = {}
        # 用于收集完整的工具调用信息
        complete_tool_calls = {}

        async for chunk in openai_stream:
            chunk_count += 1
            #print(f"Processing chunk {chunk_count}: {chunk}")

            if hasattr(chunk, 'choices') and chunk.choices:
                delta = chunk.choices[0].delta

                # 处理文本内容
                if hasattr(delta, 'content') and delta.content:
                    has_content = True
                    payload = {
                        "type": "content_block_delta",
                        "index": 0,
                        "delta": {"type": "text_delta", "text": delta.content}
                    }
                    #print(f"Sending text delta: {payload}")
                    yield f"event: content_block_delta\ndata: {json.dumps(payload)}\n\n"

                # 处理工具调用
                if hasattr(delta, 'tool_calls') and delta.tool_calls:
                    for tc in delta.tool_calls:
                        tc_id = getattr(tc, 'id', None)

                        # 如果是新的工具调用，初始化
                        if tc_id and tc_id not in current_tool_calls:
                            current_tool_calls[tc_id] = {
                                "index": tool_call_index,
                                "name": "",
                                "arguments": ""
                            }
                            complete_tool_calls[tc_id] = {
                                "id": tc_id,
                                "name": "",
                                "arguments": ""
                            }

                            if hasattr(tc, 'function') and hasattr(tc.function, 'name') and tc.function.name:
                                current_tool_calls[tc_id]["name"] = tc.function.name
                                complete_tool_calls[tc_id]["name"] = tc.function.name

                                tool_start = {
                                    "type": "content_block_start",
                                    "index": tool_call_index,
                                    "content_block": {
                                        "type": "tool_use",
                                        "id": tc_id,
                                        "name": tc.function.name,
                                        "input": {}
                                    }
                                }
                                print(f"Sending tool_start: {tool_start}")
                                yield f"event: content_block_start\ndata: {json.dumps(tool_start)}\n\n"
                                tool_call_index += 1

                        # 处理工具参数增量
                        if tc_id and hasattr(tc, 'function') and hasattr(tc.function, 'arguments') and tc.function.arguments:
                            current_tc = current_tool_calls.get(tc_id)
                            if current_tc:
                                tool_delta = {
                                    "type": "content_block_delta",
                                    "index": current_tc["index"],
                                    "delta": {"type": "input_json_delta", "partial_json": tc.function.arguments}
                                }
                                #print(f"Sending tool_delta: {tool_delta}")
                                yield f"event: content_block_delta\ndata: {json.dumps(tool_delta)}\n\n"
                                current_tc["arguments"] += tc.function.arguments
                                complete_tool_calls[tc_id]["arguments"] += tc.function.arguments

        print(f"Processed {chunk_count} chunks total")

        # 发送content_block_stop事件 - 先停止文本块
        if has_content:
            content_stop = {"type": "content_block_stop", "index": 0}
            #print(f"Sending content_block_stop: {content_stop}")
            yield f"event: content_block_stop\ndata: {json.dumps(content_stop)}\n\n"

        # 停止所有工具调用块，并确保参数完整性
        for tc_id, tc_info in current_tool_calls.items():
            # 验证工具调用参数的完整性
            complete_tc = complete_tool_calls.get(tc_id, {})
            arguments_str = complete_tc.get("arguments", "")

            # 尝试解析JSON以验证完整性
            try:
                if arguments_str and arguments_str.strip():
                    parsed_args = json.loads(arguments_str)
                    print(f"Tool call {tc_id} ({complete_tc.get('name', 'unknown')}) arguments parsed successfully: {parsed_args}")
                else:
                    print(f"Warning: Tool call {tc_id} has empty arguments")
            except json.JSONDecodeError as e:
                print(f"Warning: Tool call {tc_id} has invalid JSON arguments: {arguments_str}, error: {e}")
                # 如果JSON不完整，尝试修复
                if arguments_str and not arguments_str.strip().endswith('}'):
                    print(f"Attempting to fix incomplete JSON for tool {tc_id}")
                    # 这里可以添加更复杂的JSON修复逻辑

            tool_stop = {"type": "content_block_stop", "index": tc_info["index"]}
            print(f"Sending tool_block_stop: {tool_stop}")
            yield f"event: content_block_stop\ndata: {json.dumps(tool_stop)}\n\n"

        # 发送message_delta事件
        message_delta = {
            "type": "message_delta",
            "delta": {"stop_reason": "end_turn", "stop_sequence": None},
            "usage": {"output_tokens": chunk_count}
        }
        #print(f"Sending message_delta: {message_delta}")
        yield f"event: message_delta\ndata: {json.dumps(message_delta)}\n\n"

        # 发送最终事件
        final_event = {"type": "message_stop"}
        print(f"Sending final_event: {final_event}")
        yield f"event: message_stop\ndata: {json.dumps(final_event)}\n\n"

        print("Stream conversion completed successfully")

    except Exception as e:
        print(f"Error in stream conversion: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        error_event = {
            "type": "error",
            "error": {
                "type": "api_error",
                "message": str(e)
            }
        }
        yield f"event: error\ndata: {json.dumps(error_event)}\n\n"
openai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY","sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB"),
                            base_url=os.getenv("OPENAI_BASE_URL", "https://ai.secsign.online:3003/apiconvert/v1"))

app = FastAPI(title="Anthropic to OpenAI Bridge", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# 
async def collect_stream_response(model_name, **kwargs):
    # 非流式请求但强制使用流式处理
    kwargs["stream"] = True
    openai_stream = await openai_client.chat.completions.create(**kwargs)
    # 收集流式响应并组装为非流式格式
    collected_content = ""
    collected_tool_calls = []
    finish_reason = None
    usage_info = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
    
    async for chunk in openai_stream:
        if hasattr(chunk, 'choices') and chunk.choices:
            choice = chunk.choices[0]
            delta = choice.delta
            
            # 收集文本内容
            if hasattr(delta, 'content') and delta.content:
                collected_content += delta.content
            
            # 收集工具调用
            if hasattr(delta, 'tool_calls') and delta.tool_calls:
                for tc_delta in delta.tool_calls:
                    # 查找或创建对应的工具调用
                    existing_tc = None
                    if hasattr(tc_delta, 'id') and tc_delta.id:
                        for tc in collected_tool_calls:
                            if tc["id"] == tc_delta.id:
                                existing_tc = tc
                                break
                        
                        if not existing_tc:
                            existing_tc = {
                                "id": tc_delta.id,
                                "type": "function",
                                "function": {"name": "", "arguments": ""}
                            }
                            collected_tool_calls.append(existing_tc)
                    
                    if existing_tc and hasattr(tc_delta, 'function'):
                        if hasattr(tc_delta.function, 'name') and tc_delta.function.name:
                            existing_tc["function"]["name"] = tc_delta.function.name
                        if hasattr(tc_delta.function, 'arguments') and tc_delta.function.arguments:
                            existing_tc["function"]["arguments"] += tc_delta.function.arguments
            
            # 获取完成原因
            if hasattr(choice, 'finish_reason') and choice.finish_reason:
                finish_reason = choice.finish_reason
        
        # 收集使用信息
        if hasattr(chunk, 'usage') and chunk.usage:
            if hasattr(chunk.usage, 'prompt_tokens'):
                usage_info["prompt_tokens"] = chunk.usage.prompt_tokens
            if hasattr(chunk.usage, 'completion_tokens'):
                usage_info["completion_tokens"] = chunk.usage.completion_tokens
            if hasattr(chunk.usage, 'total_tokens'):
                usage_info["total_tokens"] = chunk.usage.total_tokens
    
    # 组装为标准的OpenAI响应格式
    mock_response = {
        "id": f"chatcmpl-{uuid.uuid4().hex}",
        "object": "chat.completion",
        "created": int(time.time()),
        "model": model_name,
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": collected_content if collected_content else None
            },
            "finish_reason": finish_reason or "stop"
        }],
        "usage": usage_info
    }
    
    # 如果有工具调用，添加到消息中
    if collected_tool_calls:
        # 对收集到的工具调用参数进行JSON解析处理
        for tc in collected_tool_calls:
            if tc.get("function", {}).get("arguments"):
                arguments_str = tc["function"]["arguments"]
                try:
                    if arguments_str and arguments_str.strip():
                        # 验证JSON格式是否正确
                        json.loads(arguments_str)
                    else:
                        # 如果为空，设置为空对象的JSON字符串
                        tc["function"]["arguments"] = "{}"
                except json.JSONDecodeError as e:
                    print(f"Warning: Invalid JSON in tool arguments: {arguments_str}, error: {e}")
                    # 如果JSON无效，设置为空对象
                    tc["function"]["arguments"] = "{}"
        
        mock_response["choices"][0]["message"]["tool_calls"] = collected_tool_calls
    
    # 创建一个mock对象来模拟OpenAI响应的model_dump方法
    class MockResponse:
        def __init__(self, data):
            self.data = data
        
        def model_dump(self):
            return self.data
    
    return MockResponse(mock_response)

#@app.middleware("http")
async def log_requests(request: Request, call_next):
    print(f"Request: {request.method} {request.url}")
    print(f"Headers: {dict(request.headers)}")
    try:
        body = await request.body()
        if body:
            print(f"Body: {body.decode('utf-8')[:500]}..." if len(body) > 500 else f"Body: {body.decode('utf-8')}")
    except Exception as e:
        print(f"Error reading body: {e}")
    
    response = await call_next(request)
    print(f"Response status: {response.status_code}")
    return response

@app.post("/v1/messages")
async def anthropic_endpoint(request: Request):
    try:
        # 手动解析请求体以便更好地处理错误
        body = await request.body()
        if not body:
            raise HTTPException(status_code=400, detail="Empty request body")
        
        try:
            req_data = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON: {str(e)}")
        
        # 打印请求数据以便调试
        #print(f"Parsed request data: {json.dumps(req_data, indent=2, ensure_ascii=False)}")
        
        # 验证请求数据并创建AnthropicRequest对象
        try:
            req = AnthropicRequest(**req_data)
        except Exception as e:
            print(f"Validation error details: {str(e)}")
            print(f"Request data keys: {list(req_data.keys()) if isinstance(req_data, dict) else 'Not a dict'}")
            if isinstance(req_data, dict):
                if 'system' in req_data:
                    print(f"System field type: {type(req_data['system'])}, value: {req_data['system']}")
                if 'tools' in req_data:
                    print(f"Tools field type: {type(req_data['tools'])}, length: {len(req_data['tools']) if req_data['tools'] else 0}")
                    if req_data['tools']:
                        for i, tool in enumerate(req_data['tools'][:3]):  # 只打印前3个工具
                            print(f"Tool {i}: {json.dumps(tool, indent=2, ensure_ascii=False)}")
            raise HTTPException(status_code=400, detail=f"Invalid request format: {str(e)}")
        openai_msgs = anthropic_to_openai_messages(req)
        tools = anthropic_to_openai_tools(req)
        
        #print(f"Converted OpenAI messages: {json.dumps(openai_msgs, indent=2, ensure_ascii=False)}")
        # if tools:
        #     print(f"Converted OpenAI tools: {json.dumps(tools, indent=2, ensure_ascii=False)}")
        
        kwargs = {
            "model": req.model,
            "messages": openai_msgs,
            "max_tokens": req.max_tokens,
            "temperature": req.temperature,
            "stream": req.stream,
        }
        if tools:
            kwargs["tools"] = tools
            if req.tool_choice and req.tool_choice != "auto":
                kwargs["tool_choice"] = req.tool_choice

        if req.stream:
            openai_stream = await openai_client.chat.completions.create(**kwargs)
            return StreamingResponse(
                openai_to_anthropic_stream(openai_stream, req.model),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive"
                }
            )
        else:
            openai_resp = await openai_client.chat.completions.create(**kwargs)
            #openai_resp = await collect_stream_response(req.model, **kwargs)
            return openai_to_anthropic_response(openai_resp.model_dump(), req.model)
    
    except HTTPException:
        raise
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# 添加健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "Anthropic to OpenAI bridge is running"}

@app.get("/")
async def root():
    return {"message": "Anthropic to OpenAI API Bridge", "version": "1.0.0"}

if __name__ == "__main__":
    import uvicorn
    port = 8100
    print("Starting Anthropic to OpenAI Bridge Server...")
    print(f"Server will be available at http://0.0.0.0:{port}")
    print(f"Health check: http://0.0.0.0:{port}/health")
    print(f"Messages endpoint: http://0.0.0.0:{port}/v1/messages")
    uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")