import re
from claude_code_sdk import ClaudeSDKClient, ClaudeCodeOptions
from ..config.settings import get_settings
from typing import Dict, List, Optional, Any, Callable, Union
from loguru import logger

class ClaudeAgent:
    """ClaudeCode 智能体"""
    def __init__(self, enable_session: bool = True):
        self.settings = get_settings()
        self.enable_session = enable_session
        self._init_mcp_server()
    
    def _init_mcp_server(self):
        self.mcp_servers = {
            # Example configuration - uncomment and configure as needed:
            # "search-api": {
            #     "command": "python",
            #     "args": [
            #         "/mnt/d/aitools/restAgent/mcp_search_api_server.py"
            #     ],
            #     "env": {
            #         "PYTHONPATH": "/mnt/d/aitools/restAgent"
            #     }
            # }
            "search-api": {
                "type": "sse",
                "url": "http://127.0.0.1:7001/sse",
                "timeout": 120
            }
        }
    
    async def build_app(self, session_id: str,
        app_id: Optional[str],
        app_dir: str,
        is_new_app : bool,
        name: str,
        description: str,
        requirements: str,
        iteration_history: Optional[List[Dict[str, Any]]] = None,
        stream_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        system_prompt = """
- 分析用户需求生成个性化APP
- APP只有前端WEB页面，并通过REST API调用后台服务
- 只能使用从知识库搜索到的相关REST API
- 前端界面技术栈要求使用Bootstrap5、jQuery和Ajax的响应式Web界面
- 调用后端的js脚本、css样式都在一个html文件中
- 请将生成的前端代码做为响应输出
- 请始终用中文回答。
        """
        async with ClaudeSDKClient(
            options=ClaudeCodeOptions(
                system_prompt=system_prompt,
                resume=None,
                max_turns=20,
                cwd=app_dir,
                permission_mode = "bypassPermissions",
                #mcp_servers=self.mcp_servers,
                max_thinking_tokens=0,
                allowed_tools=["mcp__search-api"],
                continue_conversation=True,
                extra_args={
                    "verbose": None,
                    "output-format": "stream-json"
                }
            )
        ) as client:
            # Send the query
            await client.query(requirements + " /nothink")

            # Stream the response
            result = ""
            async for message in client.receive_response():
                if hasattr(message, 'content'):
                    # Print streaming content as it arrives
                    for block in message.content:
                        if hasattr(block, 'type'):
                            if block.type == 'tool_use':
                                print(f"\n[Using tool: {block.name}]\n")
                            elif hasattr(block, 'text'):
                                print(block.text, end='', flush=True)
                        elif hasattr(block, 'text'):
                            print(block.text, end='', flush=True)
                if type(message).__name__ == "ResultMessage":
                    result = message.result
            
            # if "error" in result:
            #     raise Exception(result)
            
            # 读取app目录下的index.html文件
            app_content = open(app_dir + "/index.html", "r", encoding="utf-8").read()
            if not app_content:
                # 如果没有成功写入文件，则使用result作为app内容
                logger.warning("APP文件未成功写入！")
                app_content = self.remove_think(result)
                html_content = self.extract_html_content(app_content)
                #html_content = self.sanitize_html_content(html_content)
                open(app_dir + "/index.html", "w", encoding="utf-8").write(html_content)
            
            return {
                "app_id": app_id,
                "content": app_content,
                "message": "APP构建成功" if is_new_app else "APP更新成功"
            }
    def remove_think(self, text):
        """
        移除字符串中所有<ai>和</ai>标签及其之间的内容。

        参数:
            text (str): 输入的字符串

        返回:
            str: 处理后的字符串
        """
        pattern = r'<think>.*?</think>'
        while True:
            new_text = re.sub(pattern, '', text, flags=re.DOTALL)
            if new_text == text:
                break
            text = new_text
        if all(c in ('\n', ' ') for c in text):
            text = text.replace('\n', '').replace(' ', '')
        return text
    def extract_html_content(self, content: str) -> str:
        """提取HTML内容"""
        # 查找HTML代码块
        import re
        
        # 尝试提取```html代码块
        html_match = re.search(r'```html\s*(.*?)\s*```', content, re.DOTALL | re.IGNORECASE)
        if html_match:
            return html_match.group(1).strip()
        
        # 尝试提取```代码块
        code_match = re.search(r'```\s*(.*?)\s*```', content, re.DOTALL)
        if code_match:
            code_content = code_match.group(1).strip()
            # 检查是否是HTML内容
            if '<!DOCTYPE html>' in code_content or '<html' in code_content:
                return code_content
        
        # 如果没有代码块，查找HTML文档
        if '<!DOCTYPE html>' in content or '<html' in content:
            # 提取从<!DOCTYPE html>或<html>开始到</html>结束的内容
            start_match = re.search(r'(<!DOCTYPE html>|<html[^>]*>)', content, re.IGNORECASE)
            if start_match:
                start_pos = start_match.start()
                end_match = re.search(r'</html>', content[start_pos:], re.IGNORECASE)
                if end_match:
                    end_pos = start_pos + end_match.end()
                    return content[start_pos:end_pos]
        
        # 如果都没找到，返回原内容（可能需要进一步处理）
        return content
    def sanitize_html_content(self, content: str) -> str:
        """清理HTML内容"""
        # 基本的HTML清理，移除潜在的危险标签
        import re
        
        # 移除script标签
        content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
        
        # 移除危险的事件处理器
        dangerous_attrs = ['onload', 'onclick', 'onmouseover', 'onerror', 'onsubmit']
        for attr in dangerous_attrs:
            content = re.sub(f'{attr}\\s*=\\s*["\'][^"\']*["\']', '', content, flags=re.IGNORECASE)
        
        return content