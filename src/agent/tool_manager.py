import logging
import subprocess
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Union
from loguru import logger
from ..config.settings import get_settings
from .llm_client import LLMClient

@dataclass
class ToolCall:
    """工具调用数据结构"""
    id: str
    name: str
    parameters: Dict[str, Any]
    result: Optional[str] = None
    error: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class Tool(ABC):
    """工具基类"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        pass
    
    @abstractmethod
    async def execute(self, parameters: Any) -> str:
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """返回工具的JSON Schema"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self._get_parameters_schema()
            }
        }
    
    @abstractmethod
    def _get_parameters_schema(self) -> Dict[str, Any]:
        pass

class BashTool(Tool):
    """Bash命令执行工具"""
    
    @property
    def name(self) -> str:
        return "bash"
    
    @property
    def description(self) -> str:
        return "Execute bash commands in the terminal"
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "command": {
                    "type": "string",
                    "description": "The bash command to execute"
                }
            },
            "required": ["command"]
        }
    
    async def execute(self, parameters: Dict[str, Any]) -> str:
        command = parameters.get("command")
        if not command:
            raise ValueError("Command parameter is required")
        
        try:
            # 安全检查
            if any(dangerous in command for dangerous in ["rm -rf", "sudo", "passwd"]):
                return "Error: Dangerous command detected and blocked"
            
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            output = result.stdout
            if result.stderr:
                output += f"\nSTDERR: {result.stderr}"
            if result.returncode != 0:
                output += f"\nReturn code: {result.returncode}"
                
            return output
            
        except subprocess.TimeoutExpired:
            return "Error: Command timed out after 30 seconds"
        except Exception as e:
            return f"Error executing command: {str(e)}"

class FileOperationTool(Tool):
    """文件操作工具"""
    
    @property
    def name(self) -> str:
        return "file_operation"
    
    @property
    def description(self) -> str:
        return "Read, write, and search files"
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "operation": {
                    "type": "string",
                    "enum": ["read", "write", "search", "list"],
                    "description": "The file operation to perform"
                },
                "path": {
                    "type": "string",
                    "description": "The file or directory path"
                },
                "content": {
                    "type": "string",
                    "description": "Content to write (for write operation)"
                },
                "pattern": {
                    "type": "string",
                    "description": "Search pattern (for search operation)"
                }
            },
            "required": ["operation", "path"]
        }
    
    async def execute(self, parameters: Dict[str, Any]) -> str:
        operation = parameters.get("operation")
        path = parameters.get("path")
        
        try:
            if operation == "read":
                with open(path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            elif operation == "write":
                content = parameters.get("content", "")
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return f"Successfully wrote to {path}"
            
            elif operation == "list":
                path_obj = Path(path)
                if path_obj.is_dir():
                    files = [str(p) for p in path_obj.iterdir()]
                    return "\n".join(files)
                else:
                    return f"Error: {path} is not a directory"
            
            elif operation == "search":
                pattern = parameters.get("pattern")
                if not pattern:
                    return "Error: Pattern parameter is required for search"
                
                # 简单的grep实现
                result = subprocess.run(
                    ["grep", "-r", pattern, path],
                    capture_output=True,
                    text=True
                )
                return result.stdout if result.stdout else "No matches found"
                
        except FileNotFoundError:
            return f"Error: File {path} not found"
        except PermissionError:
            return f"Error: Permission denied for {path}"
        except Exception as e:
            return f"Error: {str(e)}"

class PermissionManager:
    """权限管理器"""
    
    def __init__(self):
        self.allowed_tools: List[str] = []
        self.denied_tools: List[str] = []
        self.ask_permission_tools: List[str] = []
        self.confirmation_callback: Optional[Callable[[str, Dict], bool]] = None
    
    def set_allowed_tools(self, tools: List[str]):
        """设置允许的工具列表"""
        self.allowed_tools = tools
    
    def set_denied_tools(self, tools: List[str]):
        """设置禁止的工具列表"""
        self.denied_tools = tools
    
    def set_ask_permission_tools(self, tools: List[str]):
        """设置需要询问权限的工具列表"""
        self.ask_permission_tools = tools
    
    def set_confirmation_callback(self, callback: Callable[[str, Dict], bool]):
        """设置权限确认回调函数"""
        self.confirmation_callback = callback
    
    async def check_permission(self, tool_name: str, parameters: Dict[str, Any]) -> bool:
        """检查工具使用权限"""
        return True
        if tool_name in self.denied_tools:
            return False
        
        if tool_name in self.allowed_tools:
            return True
        
        if tool_name in self.ask_permission_tools:
            if self.confirmation_callback:
                return self.confirmation_callback(tool_name, parameters)
            else:
                # 默认询问用户
                print(f"Tool '{tool_name}' wants to execute with parameters: {parameters}")
                response = input("Allow this tool execution? (y/n): ")
                return response.lower() == 'y'
        
        # 默认允许
        return True

class ToolManager:
    """工具管理器"""
    
    def __init__(self, default_tools:bool = False):
        self.tools: Dict[str, Tool] = {}
        if default_tools:
            self._register_tools()
    
    def _register_tools(self):
        """注册所有工具"""
        self.add_tool(BashTool())
        self.add_tool(FileOperationTool())
    def add_tool(self, tool: Tool):
        """添加新工具"""
        self.tools[tool.name] = tool
        logger.info(f"Added tool: {tool.name}")
    def remove_tool(self, tool_name: str):
        """移除工具"""
        if tool_name in self.tools:
            del self.tools[tool_name]
            logger.info(f"Removed tool: {tool_name}")
    def list_tools(self) -> List[Dict[str, Any]]:
        """获取所有工具的Schema"""
        return [tool.get_schema() for tool in self.tools.values()]
    
    async def execute_tool(self, tool_call: ToolCall) -> str:
        """执行工具调用"""
        tool_name = tool_call.name
        
        if tool_name not in self.tools:
            error_msg = f"Tool '{tool_name}' not found"
            tool_call.error = error_msg
            return error_msg
        
        # 检查权限
        has_permission = True
        
        if not has_permission:
            error_msg = f"Permission denied for tool '{tool_name}'"
            tool_call.error = error_msg
            return error_msg
        
        try:
            tool = self.tools[tool_name]
            result = await tool.execute(tool_call.parameters)
            tool_call.result = result
            return result
        except Exception as e:
            error_msg = f"Error executing tool '{tool_name}': {str(e)}"
            tool_call.error = error_msg
            logger.error(error_msg)
            return error_msg