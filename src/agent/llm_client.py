"""
大模型客户端
"""

import json
import re
import asyncio
import time
from typing import List, Dict, Any, Optional, Union, Callable
import openai
from loguru import logger
from ..config.settings import get_settings


class LLMClient:
    """大模型客户端"""
    
    def __init__(self):
        self.settings = get_settings()
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化大模型客户端"""
        try:
            if self.settings.llm.base_url():
                client_kwargs = {
                    "api_key": self.settings.llm.api_key(),
                    "timeout": 50,
                    "base_url": self.settings.llm.base_url().rstrip("/") + "/"
                }
                self.client = openai.AsyncOpenAI(**client_kwargs)
                logger.info("已初始化OpenAI大模型客户端")
            else:
                raise ValueError(f"不支持的大模型提供商: {self.settings.llm.provider}")
                
        except Exception as e:
            logger.error(f"初始化大模型客户端失败: {e}")
            raise
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        stream_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        聊天完成

        Args:
            messages: 消息列表
            tools: 工具列表
            tool_choice: 工具选择策略
            stream_callback: 流式回调函数，用于实时处理流式数据

        Returns:
            Dict: 响应结果
        """
        try:
            stream = True
            kwargs = {
                "model": self.settings.llm.model_name(),
                "messages": messages,
                "temperature": self.settings.llm.temperature,
                "max_tokens": self.settings.llm.max_tokens,
                "stream": stream
            }
            
            # 添加工具相关参数
            if tools:
                kwargs["tools"] = tools
                if tool_choice:
                    kwargs["tool_choice"] = tool_choice
            
            #response = openai.chat.completions.create(**kwargs)
            response = await self.client.chat.completions.create(**kwargs)
            tool_calls = []
            if stream:
                content = await self.process_stream_response(response, tool_calls, stream_callback)
            else:
                # 非流式响应
                resp_msg = response.choices[0].message
                content=resp_msg.content
           
                if resp_msg.tool_calls:
                    tool_calls=[
                        {
                                "id": tc.id,
                                "function": {
                                    "name": tc.function.name,
                                    "arguments": json.loads(tc.function.arguments) if tc.function.arguments else ""
                                }
                                ,
                                "type": "function"
                            } for tc in resp_msg.tool_calls
                    ]
            
            # 转换tool_calls的格式为openai兼容
            openai_tools = []
            for tc in tool_calls:
                function_data = tc.get("function")
                if function_data and function_data.get("name"):
                    openai_tools.append(tc)
                else:
                    openai_tools.append(
                        {
                            "id": tc.get("id",f"call_{int(time.time() * 1000)}"),
                            "type": "function",
                            "function": {
                                "name": tc.get("name"),
                                "arguments": f'{tc.get("arguments")}'
                            }
                        }
                    )
            
            content = self.remove_think(content)
            # 转换响应格式
            result = {
                #response id或者自动生成
                "id": f"{int(time.time() * 1000 )}",
                "content": content,
                "tool_calls": openai_tools
                # "model": response.model,
                # "choices": []
            }
            
            # logger.debug(f"大模型响应: {choice.finish_reason}")
            return result
            
        except Exception as e:
            logger.error(f"大模型调用失败: {str(e)}")
            return {
                "error": str(e),
                "content": []
            }
    
    async def process_stream_response(self, response, tool_calls: List[Any],stream_callback: Optional[Callable] = None):
        # 流式响应处理
        content = ""
        stream_tool = None
        tool_args = ""
        async for chunk in response:
            if chunk.choices[0].delta.content:
                chunk_content = chunk.choices[0].delta.content
                #print(chunk_content)
                content += chunk_content
                # 如果有回调函数，实时调用
                if stream_callback:
                    try:
                        # 检查stream_callback是否为异步生成器
                        callback_result = stream_callback(chunk_content)
                        if hasattr(callback_result, '__aiter__'):
                            # 如果是异步生成器，需要异步迭代消费它
                            async for _ in callback_result:
                                pass
                        elif asyncio.iscoroutine(callback_result):
                            # 如果是协程，等待它完成
                            await callback_result
                    except Exception as e:
                        logger.warning(f"流式回调函数执行失败: {e}")
            elif chunk.choices[0].delta.tool_calls:
                # 流式工具调用的输出
                for tc in chunk.choices[0].delta.tool_calls:
                    #print(f"**::{tc}")
                    if tc.function and tc.function.name:
                        # 新工具名称的开始
                        if stream_tool:
                            # 保存原来的stream_tool
                            stream_tool = self.parse_stream_tool(stream_tool, tool_args)
                            tool_calls.append(stream_tool)
                            stream_tool = None
                            tool_args = ""
                        
                        stream_tool = {
                                "id": tc.id,
                                "function": {
                                    "name": tc.function.name,
                                    "arguments": None
                                },
                                "type": "function"
                        }
                        tool_args = tc.function.arguments if tc.function and tc.function.arguments else ""
                    
                    elif tc.function and tc.function.arguments:
                        tool_args += tc.function.arguments
                
        if stream_tool:
            stream_tool = self.parse_stream_tool(stream_tool, tool_args)
            tool_calls.append(stream_tool)

        #llm_response.response_time = time.time() - start_time
                        
        if not tool_calls:
            # 解析所有工具调用
            try:
                content = self._parse_tool_call(content, tool_calls, 'tool_call')
                content = self._parse_json_tool_call(content, tool_calls)
            except json.JSONDecodeError as e:
                logger.error(f"解析工具调用JSON失败: {content}")
                content = f"解析工具调用JSON失败: {content}"
        
        return content

    def parse_stream_tool(self, stream_tool, tool_args):
        if stream_tool:
            if tool_args:
                try:
                    stream_tool["function"]["arguments"] = json.loads(tool_args.strip())
                except json.JSONDecodeError as e:
                    logger.warning(f"json解析工具调用参数失败: {tool_args}")
                    stream_tool["function"]["arguments"] = tool_args
        return stream_tool

    def remove_think(self, text):
        """
        移除字符串中所有<ai>和</ai>标签及其之间的内容。

        参数:
            text (str): 输入的字符串

        返回:
            str: 处理后的字符串
        """
        pattern = r'<think>.*?</think>'
        while True:
            new_text = re.sub(pattern, '', text, flags=re.DOTALL)
            if new_text == text:
                break
            text = new_text
        if all(c in ('\n', ' ') for c in text):
            text = text.replace('\n', '').replace(' ', '')
        return text
    def _parse_tool_call(self, content:str , tool_calls: List[Any], pattern='tool_call') ->str:
        call_pattern=f'<{pattern}>(.*?)</{pattern}>'
        tool_calls_matches = re.findall(call_pattern, content, re.DOTALL)
        for match in tool_calls_matches:
            # 逐个解析JSON并合并
            parsed = json.loads(match.strip())
            if isinstance(parsed, list):
                tool_calls.extend(parsed)
            else:
                tool_calls.append(parsed)
            content = content.replace(f'<{pattern}>{match}</{pattern}>', '')
        return content
    def _parse_json_tool_call(self, content: str, tool_calls: List[Any]) -> str:
        # "\n\n{'tool_calls': [{'name': 'search_code', 'arguments': {'query': 'RedisConnection::run', 'top_k': 5}, 'id': 'call_3'}]}"
        # 检查如果包含tool_calls，则使用json解析
        if "tool_calls" in content:
            json_start = content.find('{')
            json_end = content.rfind('}') + 1
            json_content = content[json_start:json_end]
            # 单引号替换为双引号
            json_content = json_content.replace("'", '"')
            json_tools = json.loads(json_content).get("tool_calls")
            if isinstance(json_tools, list):
                tool_calls.extend(json_tools)
            else:
                tool_calls.append(json_tools)
            return content[:json_start]
        else:
            return content
    def create_tool_definition(self, api_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据API数据创建工具定义
        
        Args:
            api_data: API数据
            
        Returns:
            Dict: 工具定义
        """
        try:
            tool_name = self._generate_tool_name(api_data)
            description = self._generate_tool_description(api_data)
            parameters = self._generate_tool_parameters(api_data)
            
            tool_definition = {
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": description,
                    "parameters": parameters
                }
            }
            
            return tool_definition
            
        except Exception as e:
            logger.error(f"创建工具定义失败: {e}")
            return {}
    
    def _generate_tool_name(self, api_data: Dict[str, Any]) -> str:
        """生成工具名称"""
        try:
            # 使用API路径和方法生成工具名称
            path = api_data.get("path", "").replace("/", "_").replace("{", "").replace("}", "")
            method = api_data.get("method", "get").lower()
            
            # 清理路径中的特殊字符
            path = "".join(c for c in path if c.isalnum() or c == "_")
            
            tool_name = f"{method}_{path}".strip("_")
            
            # 确保工具名称不为空且符合规范
            if not tool_name or tool_name == method:
                api_id = api_data.get("_id", "")
                tool_name = f"{method}_api_{api_id}"
            
            return tool_name
            
        except Exception as e:
            logger.error(f"生成工具名称失败: {e}")
            return f"api_tool_{api_data.get('_id', 'unknown')}"
    
    def _generate_tool_description(self, api_data: Dict[str, Any]) -> str:
        """生成工具描述"""
        try:
            parts = []
            
            title = api_data.get("title", "")
            if title:
                parts.append(title)
            
            desc = api_data.get("desc", "")
            if desc:
                parts.append(desc)
            
            method = api_data.get("method", "").upper()
            path = api_data.get("path", "")
            if method and path:
                parts.append(f"请求方式: {method} {path}")
            
            catname = api_data.get("catname", "")
            if catname:
                parts.append(f"分类: {catname}")
            
            return " | ".join(parts) if parts else "API接口调用"
            
        except Exception as e:
            logger.error(f"生成工具描述失败: {e}")
            return "API接口调用"
    
    def _generate_tool_parameters(self, api_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成工具参数定义"""
        try:
            parameters = {
                "type": "object",
                "properties": {},
                "required": []
            }
            
            # 添加路径参数
            req_params = api_data.get("req_params", [])
            for param in req_params:
                name = param.get("name")
                if name:
                    parameters["properties"][name] = {
                        "type": "string",
                        "description": param.get("desc", f"路径参数: {name}")
                    }
                    parameters["required"].append(name)
            
            # 添加查询参数
            req_query = api_data.get("req_query", [])
            for param in req_query:
                name = param.get("name")
                if name:
                    parameters["properties"][name] = {
                        "type": "string",
                        "description": param.get("desc", f"查询参数: {name}")
                    }
                    if param.get("required"):
                        parameters["required"].append(name)
            
            # 添加请求体参数
            req_body_type = api_data.get("req_body_type", "")
            if req_body_type == "json" and api_data.get("req_body_other"):
                try:
                    body_schema = json.loads(api_data["req_body_other"])
                    if isinstance(body_schema, dict) and "properties" in body_schema:
                        # 合并JSON Schema中的属性
                        for prop_name, prop_def in body_schema["properties"].items():
                            parameters["properties"][prop_name] = prop_def
                        
                        # 添加必需字段
                        if "required" in body_schema:
                            parameters["required"].extend(body_schema["required"])
                except json.JSONDecodeError:
                    # 如果不是有效的JSON Schema，添加一个通用的body参数
                    parameters["properties"]["request_body"] = {
                        "type": "string",
                        "description": "请求体JSON字符串"
                    }
            
            elif req_body_type == "form":
                req_body_form = api_data.get("req_body_form", [])
                for form in req_body_form:
                    name = form.get("name")
                    if name:
                        parameters["properties"][name] = {
                            "type": "string",
                            "description": form.get("desc", f"表单参数: {name}")
                        }
                        if form.get("required"):
                            parameters["required"].append(name)
            
            return parameters
            
        except Exception as e:
            logger.error(f"生成工具参数失败: {e}")
            return {
                "type": "object",
                "properties": {},
                "required": []
            }
