"""
REST API调用器
"""

import json
from typing import Dict, Any, Optional
import httpx
from loguru import logger
from ..config.settings import get_settings


class ApiCaller:
    """REST API调用器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client = httpx.Client(timeout=30.0, verify=False )
    
    def call_api(
        self,
        api_data: Dict[str, Any],
        parameters: Dict[str, Any],
        base_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        调用REST API
        
        Args:
            api_data: API定义数据
            parameters: 调用参数
            base_url: 基础URL
            
        Returns:
            Dict: 调用结果
        """
        try:
            # 构建请求URL
            url = self._build_url(api_data, parameters, base_url)
            
            # 构建请求头
            headers = self._build_headers(api_data, parameters)
            
            # 构建请求参数
            query_params = self._build_query_params(api_data, parameters)
            
            # 构建请求体
            request_body = self._build_request_body(api_data, parameters)
            
            # 获取HTTP方法
            method = api_data.get("method", "GET").upper()
            
            logger.info(f"调用API: {method} {url}")
            logger.debug(f"请求参数: {parameters}")
            
            # 发送请求 (SSL证书验证关闭)
            response = self.client.request(
                method=method,
                url=url,
                headers=headers,
                params=query_params,
                json=request_body if isinstance(request_body, dict) else None,
                data=request_body if isinstance(request_body, str) else None
            )
            
            # 处理响应
            result = self._process_response(response)
            
            logger.info(f"API调用完成，状态码: {response.status_code}")
            return result
            
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "status_code": None,
                "response": None
            }
    
    def _build_url(
        self,
        api_data: Dict[str, Any],
        parameters: Dict[str, Any],
        base_url: Optional[str] = None
    ) -> str:
        """构建请求URL"""
        try:
            path = api_data.get("path", "")
            
            # 替换路径参数
            req_params = api_data.get("req_params", [])
            for param in req_params:
                param_name = param.get("name")
                if param_name and param_name in parameters:
                    path = path.replace(f"{{{param_name}}}", str(parameters[param_name]))
            
            # 构建完整URL
            if base_url:
                url = f"{base_url.rstrip('/')}{path}"
            else:
                # 尝试从配置中获取服务URL
                project_id = str(api_data.get("project_id", ""))
                service_config = self.settings.rest_auth.services.get(project_id)
                if service_config and service_config.get("base_url"):
                    url = f"{service_config['base_url'].rstrip('/')}{path}"
                else:
                    url = path
            
            return url
            
        except Exception as e:
            logger.error(f"构建URL失败: {e}")
            return api_data.get("path", "")
    
    def _build_headers(self, api_data: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, str]:
        """构建请求头"""
        try:
            headers = {}
            
            # 添加默认请求头
            headers.update(self.settings.rest_auth.default_headers)
            
            # 添加服务特定的请求头
            project_id = str(api_data.get("project_id", ""))
            service_config = self.settings.rest_auth.services.get(project_id)
            if service_config and service_config.get("headers"):
                headers.update(service_config["headers"])
            
            # 添加API定义中的请求头
            req_headers = api_data.get("req_headers", [])
            for header in req_headers:
                header_name = header.get("name")
                header_value = header.get("value")
                if header_name and header_value:
                    headers[header_name] = header_value
            
            # 从参数中获取请求头（如果参数名以header_开头）
            for param_name, param_value in parameters.items():
                if param_name.startswith("header_"):
                    header_name = param_name[7:]  # 移除header_前缀
                    headers[header_name] = str(param_value)
            
            return headers
            
        except Exception as e:
            logger.error(f"构建请求头失败: {e}")
            return {}
    
    def _build_query_params(self, api_data: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, str]:
        """构建查询参数"""
        try:
            query_params = {}
            
            # 从API定义中获取查询参数
            req_query = api_data.get("req_query", [])
            for param in req_query:
                param_name = param.get("name")
                if param_name and param_name in parameters:
                    query_params[param_name] = str(parameters[param_name])
            
            return query_params
            
        except Exception as e:
            logger.error(f"构建查询参数失败: {e}")
            return {}
    
    def _build_request_body(self, api_data: Dict[str, Any], parameters: Dict[str, Any]) -> Any:
        """构建请求体"""
        try:
            req_body_type = api_data.get("req_body_type", "")
            
            if req_body_type == "json":
                # JSON请求体
                body_data = {}
                
                # 如果有request_body参数，直接使用
                if "request_body" in parameters:
                    try:
                        return json.loads(parameters["request_body"])
                    except json.JSONDecodeError:
                        return parameters["request_body"]
                
                # 否则从其他参数构建JSON
                req_body_other = api_data.get("req_body_other", "")
                if req_body_other:
                    try:
                        # 尝试解析JSON Schema
                        schema = json.loads(req_body_other)
                        if isinstance(schema, dict) and "properties" in schema:
                            # 根据schema构建请求体
                            for prop_name in schema["properties"]:
                                if prop_name in parameters:
                                    body_data[prop_name] = parameters[prop_name]
                        else:
                            # 如果不是schema，直接使用
                            return schema
                    except json.JSONDecodeError:
                        # 如果不是有效JSON，返回原始字符串
                        return req_body_other
                
                return body_data if body_data else None
                
            elif req_body_type == "form":
                # 表单请求体
                form_data = {}
                req_body_form = api_data.get("req_body_form", [])
                for form in req_body_form:
                    form_name = form.get("name")
                    if form_name and form_name in parameters:
                        form_data[form_name] = str(parameters[form_name])
                
                return form_data if form_data else None
            
            return None
            
        except Exception as e:
            logger.error(f"构建请求体失败: {e}")
            return None
    
    def _process_response(self, response: httpx.Response) -> Dict[str, Any]:
        """处理响应"""
        try:
            result = {
                "success": response.is_success,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "response": None,
                "error": None
            }
            
            # 尝试解析响应体
            try:
                if response.headers.get("content-type", "").startswith("application/json"):
                    result["response"] = response.json()
                else:
                    result["response"] = response.text
            except Exception as e:
                result["response"] = response.text
                logger.warning(f"解析响应体失败: {e}")
            
            # 如果请求失败，添加错误信息
            if not response.is_success:
                result["error"] = f"HTTP {response.status_code}: {response.reason_phrase}"
            
            return result
            
        except Exception as e:
            logger.error(f"处理响应失败: {e}")
            return {
                "success": False,
                "status_code": None,
                "response": None,
                "error": str(e)
            }
    
    def close(self):
        """关闭客户端"""
        if self.client:
            self.client.close()
