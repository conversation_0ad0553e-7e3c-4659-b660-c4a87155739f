"""
YApi数据解析器
"""

import json
from typing import Dict, Any, List, Optional
from loguru import logger


class YApiParser:
    """YApi数据解析器"""
    
    @staticmethod
    def parse_interface(interface_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析接口数据，提取关键信息
        
        Args:
            interface_data: YApi接口原始数据
            
        Returns:
            Dict: 解析后的接口信息
        """
        try:
            parsed = {
                # 基本信息
                "_id": interface_data.get("_id"),
                "title": interface_data.get("title", ""),
                "path": interface_data.get("path", ""),
                "method": interface_data.get("method", "GET").upper(),
                "project_id": interface_data.get("project_id"),
                "catname": interface_data.get("catname", ""),
                "catid": interface_data.get("catid"),
                "status": interface_data.get("status", "undone"),
                "tag": interface_data.get("tag", []),
                "desc": interface_data.get("desc", ""),
                
                # 请求信息
                "req_headers": YApiParser._parse_headers(interface_data.get("req_headers", [])),
                "req_query": YApiParser._parse_query_params(interface_data.get("req_query", [])),
                "req_params": YApiParser._parse_path_params(interface_data.get("req_params", [])),
                "req_body_type": interface_data.get("req_body_type", ""),
                "req_body_form": YApiParser._parse_form_data(interface_data.get("req_body_form", [])),
                "req_body_other": interface_data.get("req_body_other", ""),
                
                # 响应信息
                "res_body_type": interface_data.get("res_body_type", ""),
                "res_body": interface_data.get("res_body", ""),
                
                # 其他信息
                "add_time": interface_data.get("add_time"),
                "up_time": interface_data.get("up_time"),
                "username": interface_data.get("username", ""),
            }
            
            # 解析请求体JSON Schema
            if parsed["req_body_type"] == "json" and parsed["req_body_other"]:
                parsed["req_body_schema"] = YApiParser._parse_json_schema(parsed["req_body_other"])
            
            # 解析响应体JSON Schema
            if parsed["res_body_type"] == "json" and parsed["res_body"]:
                parsed["res_body_schema"] = YApiParser._parse_json_schema(parsed["res_body"])
            
            return parsed
            
        except Exception as e:
            logger.error(f"解析接口数据失败: {e}")
            return interface_data
    
    @staticmethod
    def _parse_headers(headers_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """解析请求头"""
        try:
            headers = []
            for header in headers_data:
                if header.get("name"):
                    headers.append({
                        "name": header.get("name", ""),
                        "value": header.get("value", ""),
                        "desc": header.get("desc", ""),
                        "required": header.get("required", "0") == "1"
                    })
            return headers
        except Exception as e:
            logger.error(f"解析请求头失败: {e}")
            return []
    
    @staticmethod
    def _parse_query_params(query_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """解析查询参数"""
        try:
            params = []
            for param in query_data:
                if param.get("name"):
                    params.append({
                        "name": param.get("name", ""),
                        "desc": param.get("desc", ""),
                        "required": param.get("required", "0") == "1",
                        "example": param.get("example", "")
                    })
            return params
        except Exception as e:
            logger.error(f"解析查询参数失败: {e}")
            return []
    
    @staticmethod
    def _parse_path_params(params_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """解析路径参数"""
        try:
            params = []
            for param in params_data:
                if param.get("name"):
                    params.append({
                        "name": param.get("name", ""),
                        "desc": param.get("desc", ""),
                        "example": param.get("example", "")
                    })
            return params
        except Exception as e:
            logger.error(f"解析路径参数失败: {e}")
            return []
    
    @staticmethod
    def _parse_form_data(form_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """解析表单数据"""
        try:
            forms = []
            for form in form_data:
                if form.get("name"):
                    forms.append({
                        "name": form.get("name", ""),
                        "type": form.get("type", "text"),
                        "desc": form.get("desc", ""),
                        "required": form.get("required", "0") == "1",
                        "example": form.get("example", "")
                    })
            return forms
        except Exception as e:
            logger.error(f"解析表单数据失败: {e}")
            return []
    
    @staticmethod
    def _parse_json_schema(schema_str: str) -> Optional[Dict[str, Any]]:
        """解析JSON Schema"""
        try:
            if not schema_str:
                return None
            
            # 尝试解析JSON
            schema = json.loads(schema_str)
            return schema
            
        except json.JSONDecodeError:
            logger.debug("JSON Schema解析失败，可能不是有效的JSON格式")
            return None
        except Exception as e:
            logger.error(f"解析JSON Schema失败: {e}")
            return None
    
    @staticmethod
    def generate_curl_command(interface_data: Dict[str, Any], base_url: str = "") -> str:
        """
        生成curl命令
        
        Args:
            interface_data: 接口数据
            base_url: 基础URL
            
        Returns:
            str: curl命令
        """
        try:
            method = interface_data.get("method", "GET").upper()
            path = interface_data.get("path", "")
            url = f"{base_url.rstrip('/')}{path}" if base_url else path
            
            curl_parts = [f"curl -X {method}"]
            
            # 添加请求头
            headers = interface_data.get("req_headers", [])
            for header in headers:
                if header.get("name") and header.get("value"):
                    curl_parts.append(f'-H "{header["name"]}: {header["value"]}"')
            
            # 添加查询参数
            query_params = interface_data.get("req_query", [])
            if query_params:
                query_str = "&".join([
                    f"{param['name']}={param.get('example', '')}"
                    for param in query_params
                    if param.get("name")
                ])
                if query_str:
                    url += f"?{query_str}"
            
            # 添加请求体
            req_body_type = interface_data.get("req_body_type", "")
            if req_body_type == "json" and interface_data.get("req_body_other"):
                curl_parts.append('-H "Content-Type: application/json"')
                curl_parts.append(f'-d \'{interface_data["req_body_other"]}\'')
            elif req_body_type == "form":
                form_data = interface_data.get("req_body_form", [])
                for form in form_data:
                    if form.get("name"):
                        curl_parts.append(f'-d "{form["name"]}={form.get("example", "")}"')
            
            curl_parts.append(f'"{url}"')
            
            return " \\\n  ".join(curl_parts)
            
        except Exception as e:
            logger.error(f"生成curl命令失败: {e}")
            return f"curl {interface_data.get('path', '')}"
    
    @staticmethod
    def extract_api_summary(interface_data: Dict[str, Any]) -> str:
        """
        提取API摘要信息用于向量化
        
        Args:
            interface_data: 接口数据
            
        Returns:
            str: API摘要
        """
        try:
            parts = []
            
            # 基本信息
            title = interface_data.get("title", "")
            if title:
                parts.append(f"接口名称: {title}")
            
            method = interface_data.get("method", "")
            path = interface_data.get("path", "")
            if method and path:
                parts.append(f"请求方式: {method.upper()} {path}")
            
            # 描述
            desc = interface_data.get("desc", "")
            if desc:
                parts.append(f"接口描述: {desc}")
            
            # 分类
            catname = interface_data.get("catname", "")
            if catname:
                parts.append(f"分类: {catname}")
            
            # 标签
            tags = interface_data.get("tag", [])
            if tags:
                parts.append(f"标签: {', '.join(tags)}")
            
            # 请求参数摘要
            req_query = interface_data.get("req_query", [])
            if req_query:
                param_names = [param.get("name", "") for param in req_query if param.get("name")]
                if param_names:
                    parts.append(f"查询参数: {', '.join(param_names)}")
            
            # 请求体摘要
            req_body_other = interface_data.get("req_body_other", "")
            if req_body_other:
                parts.append(f"请求体示例: {req_body_other[:200]}...")  # 限制长度
            
            return "\n".join(parts)
            
        except Exception as e:
            logger.error(f"提取API摘要失败: {e}")
            return interface_data.get("title", "")
