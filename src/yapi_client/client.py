"""
YApi客户端
"""

import json
from typing import List, Dict, Any, Optional
import httpx
from loguru import logger
from ..config.settings import get_settings


class YApiClient:
    """YApi客户端"""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = self.settings.yapi.base_url.rstrip('/')
        self.session = httpx.Client(timeout=30.0)
        self.token = None
        self._login()
    
    def _login(self) -> bool:
        """登录YApi获取token"""
        try:
            login_url = f"{self.base_url}/api/user/login"
            login_data = {
                "email": self.settings.yapi.username,
                "password": self.settings.yapi.password
            }
            
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errcode") == 0:
                # 从cookie中获取token
                cookies = response.cookies
                self.token = cookies.get("_yapi_token")
                if self.token:
                    logger.info("YApi登录成功")
                    return True
                else:
                    logger.error("YApi登录失败：未获取到token")
                    return False
            else:
                logger.error(f"YApi登录失败：{result.get('errmsg', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"YApi登录异常：{e}")
            return False
    
    def get_project_info(self, project_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        获取项目信息
        
        Args:
            project_id: 项目ID，如果不提供则使用配置中的项目ID
            
        Returns:
            Optional[Dict]: 项目信息
        """
        try:
            if not project_id:
                project_id = self.settings.yapi.project_id
            
            if not project_id:
                logger.error("未提供项目ID")
                return None
            
            url = f"{self.base_url}/api/project/get"
            params = {"id": project_id}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errcode") == 0:
                logger.info(f"成功获取项目信息：{result['data'].get('name', '')}")
                return result["data"]
            else:
                logger.error(f"获取项目信息失败：{result.get('errmsg', '未知错误')}")
                return None
                
        except Exception as e:
            logger.error(f"获取项目信息异常：{e}")
            return None
    
    def get_project_categories(self, project_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取项目分类列表
        
        Args:
            project_id: 项目ID
            
        Returns:
            List[Dict]: 分类列表
        """
        try:
            if not project_id:
                project_id = self.settings.yapi.project_id
            
            if not project_id:
                logger.error("未提供项目ID")
                return []
            
            url = f"{self.base_url}/api/interface/getCatMenu"
            params = {"project_id": project_id}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errcode") == 0:
                categories = result.get("data", [])
                logger.info(f"成功获取 {len(categories)} 个分类")
                return categories
            else:
                logger.error(f"获取项目分类失败：{result.get('errmsg', '未知错误')}")
                return []
                
        except Exception as e:
            logger.error(f"获取项目分类异常：{e}")
            return []
    
    def get_category_interfaces(self, category_id: str) -> List[Dict[str, Any]]:
        """
        获取分类下的接口列表
        
        Args:
            category_id: 分类ID
            
        Returns:
            List[Dict]: 接口列表
        """
        try:
            url = f"{self.base_url}/api/interface/list_cat"
            params = {"catid": category_id}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errcode") == 0:
                interfaces = result.get("data", {}).get("list", [])
                logger.info(f"分类 {category_id} 下有 {len(interfaces)} 个接口")
                return interfaces
            else:
                logger.error(f"获取分类接口失败：{result.get('errmsg', '未知错误')}")
                return []
                
        except Exception as e:
            logger.error(f"获取分类接口异常：{e}")
            return []
    
    def get_interface_detail(self, interface_id: str) -> Optional[Dict[str, Any]]:
        """
        获取接口详细信息
        
        Args:
            interface_id: 接口ID
            
        Returns:
            Optional[Dict]: 接口详细信息
        """
        try:
            url = f"{self.base_url}/api/interface/get"
            params = {"id": interface_id}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errcode") == 0:
                interface_detail = result.get("data", {})
                logger.debug(f"成功获取接口详情：{interface_detail.get('title', '')}")
                return interface_detail
            else:
                logger.error(f"获取接口详情失败：{result.get('errmsg', '未知错误')}")
                return None
                
        except Exception as e:
            logger.error(f"获取接口详情异常：{e}")
            return None
    
    def get_all_project_interfaces(self, project_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取项目下的所有接口
        
        Args:
            project_id: 项目ID
            
        Returns:
            List[Dict]: 所有接口列表
        """
        try:
            if not project_id:
                project_id = self.settings.yapi.project_id
            
            if not project_id:
                logger.error("未提供项目ID")
                return []
            
            logger.info(f"开始获取项目 {project_id} 的所有接口")
            
            # 获取所有分类
            categories = self.get_project_categories(project_id)
            if not categories:
                logger.warning("未找到任何分类")
                return []
            
            all_interfaces = []
            
            # 遍历每个分类获取接口
            for category in categories:
                category_id = category.get("_id")
                category_name = category.get("name", "")
                
                if not category_id:
                    continue
                
                logger.info(f"正在获取分类 '{category_name}' 的接口")
                
                # 获取分类下的接口列表
                interfaces = self.get_category_interfaces(category_id)
                
                # 获取每个接口的详细信息
                for interface in interfaces:
                    interface_id = interface.get("_id")
                    if interface_id:
                        detail = self.get_interface_detail(interface_id)
                        if detail:
                            # 添加分类信息
                            detail["catname"] = category_name
                            detail["catid"] = category_id
                            all_interfaces.append(detail)
            
            logger.info(f"成功获取项目 {project_id} 的 {len(all_interfaces)} 个接口")
            return all_interfaces
            
        except Exception as e:
            logger.error(f"获取项目所有接口异常：{e}")
            return []
    
    def close(self):
        """关闭客户端"""
        if self.session:
            self.session.close()
