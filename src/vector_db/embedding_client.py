"""
嵌入模型客户端
"""

from typing import List, Union
import openai
from loguru import logger
from ..config.settings import get_settings


class EmbeddingClient:
    """嵌入模型客户端"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化嵌入模型客户端"""
        try:
            if self.settings.embedding.provider == "openai":
                # 创建OpenAI客户端实例
                self.client = openai.OpenAI(
                    api_key=self.settings.embedding.api_key,
                    base_url=self.settings.embedding.base_url
                )
                logger.info(f"已初始化嵌入模型客户端: {self.settings.embedding.base_url}")
            else:
                raise ValueError(f"不支持的嵌入模型提供商: {self.settings.embedding.provider}")

        except Exception as e:
            logger.error(f"初始化嵌入模型客户端失败: {e}")
            raise
    
    def get_embeddings(self, texts: Union[str, List[str]]) -> List[List[float]]:
        """
        获取文本嵌入向量

        Args:
            texts: 文本或文本列表

        Returns:
            List[List[float]]: 嵌入向量列表
        """
        try:
            if isinstance(texts, str):
                texts = [texts]

            if not texts:
                return []

            if self.settings.embedding.provider == "openai":
                response = self.client.embeddings.create(
                    model=self.settings.embedding.model_name,
                    input=texts
                )

                embeddings = [data.embedding for data in response.data]
                logger.debug(f"成功获取 {len(embeddings)} 个文本的嵌入向量")
                return embeddings

            else:
                raise ValueError(f"不支持的嵌入模型提供商: {self.settings.embedding.provider}")

        except Exception as e:
            logger.error(f"获取嵌入向量失败: {e}")
            return []
    
    def get_single_embedding(self, text: str) -> List[float]:
        """
        获取单个文本的嵌入向量
        
        Args:
            text: 文本
            
        Returns:
            List[float]: 嵌入向量
        """
        embeddings = self.get_embeddings([text])
        return embeddings[0] if embeddings else []
