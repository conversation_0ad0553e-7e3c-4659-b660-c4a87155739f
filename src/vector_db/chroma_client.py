"""
ChromaDB向量数据库客户端
"""

import os
import json
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings
from loguru import logger
from ..config.settings import get_settings


class ChromaDBClient:
    """ChromaDB客户端"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client = None
        self.collection = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化ChromaDB客户端"""
        try:
            # 确保持久化目录存在
            persist_dir = self.settings.vector_db.persist_directory
            if not os.path.exists(persist_dir):
                os.makedirs(persist_dir)
            
            # 创建ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path=persist_dir,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 获取或创建集合
            collection_name = self.settings.vector_db.collection_name
            try:
                self.collection = self.client.get_collection(name=collection_name)
                logger.info(f"已连接到现有集合: {collection_name}")
            except Exception:
                self.collection = self.client.create_collection(
                    name=collection_name,
                    metadata={"description": "REST API接口向量存储"}
                )
                logger.info(f"已创建新集合: {collection_name}")
                
        except Exception as e:
            logger.error(f"初始化ChromaDB客户端失败: {e}")
            raise
    
    def add_api_documents(self, apis: List[Dict[str, Any]], embeddings: Optional[List[List[float]]] = None) -> bool:
        """
        添加API文档到向量数据库

        Args:
            apis: API文档列表
            embeddings: 预计算的嵌入向量列表（可选）

        Returns:
            bool: 是否成功
        """
        try:
            if not apis:
                logger.warning("没有API文档需要添加")
                return True
            
            documents = []
            metadatas = []
            ids = []
            
            for api in apis:
                # 构建文档内容
                doc_content = self._build_document_content(api)
                documents.append(doc_content)
                
                # 构建元数据
                metadata = {
                    "api_id": str(api.get("_id", "")),
                    "title": api.get("title", ""),
                    "path": api.get("path", ""),
                    "method": api.get("method", ""),
                    "project_id": str(api.get("project_id", "")),
                    "category": api.get("catname", ""),
                    "status": api.get("status", ""),
                    "tag": json.dumps(api.get("tag", []))
                }
                metadatas.append(metadata)
                
                # 生成唯一ID
                api_id = f"{api.get('project_id', '')}_{api.get('_id', '')}"
                ids.append(api_id)            
            
            # 添加到集合，如果提供了嵌入向量则使用，否则让ChromaDB自动生成
            if embeddings:
                self.collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids,
                    embeddings=embeddings
                )
            else:
                self.collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids
                )
            
            logger.info(f"成功添加 {len(apis)} 个API文档到向量数据库")
            return True
            
        except Exception as e:
            logger.error(f"添加API文档失败: {e}")
            return False
    
    def search_apis(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """
        搜索相关的API
        
        Args:
            query: 搜索查询
            n_results: 返回结果数量
            
        Returns:
            List[Dict]: 搜索结果
        """
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                include=["documents", "metadatas", "distances"]
            )
            
            search_results = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    result = {
                        "document": doc,
                        "metadata": results["metadatas"][0][i],
                        "distance": results["distances"][0][i]
                    }
                    search_results.append(result)
            
            logger.info(f"搜索查询 '{query}' 返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"搜索API失败: {e}")
            return []
    
    def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            count = self.collection.count()
            return {
                "name": self.settings.vector_db.collection_name,
                "count": count,
                "persist_directory": self.settings.vector_db.persist_directory
            }
        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return {}
    
    def clear_collection(self) -> bool:
        """清空集合"""
        try:
            # 删除现有集合
            collection_name = self.settings.vector_db.collection_name
            self.client.delete_collection(name=collection_name)
            
            # 重新创建集合
            self.collection = self.client.create_collection(
                name=collection_name,
                metadata={"description": "REST API接口向量存储"}
            )
            
            logger.info(f"已清空集合: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            return False
    
    def _build_document_content(self, api: Dict[str, Any]) -> str:
        """
        构建API文档内容用于向量化
        
        Args:
            api: API信息
            
        Returns:
            str: 文档内容
        """
        parts = []
        
        # 基本信息
        title = api.get("title", "")
        if title:
            parts.append(f"接口名称: {title}")
        
        method = api.get("method", "")
        path = api.get("path", "")
        if method and path:
            parts.append(f"请求方式: {method.upper()} {path}")
        
        # 描述信息
        desc = api.get("desc", "")
        if desc:
            parts.append(f"接口描述: {desc}")
        
        # 分类信息
        catname = api.get("catname", "")
        if catname:
            parts.append(f"分类: {catname}")
        
        # 标签信息
        tags = api.get("tag", [])
        if tags:
            parts.append(f"标签: {', '.join(tags)}")
        
        # 请求参数
        req_body_other = api.get("req_body_other", "")
        if req_body_other:
            parts.append(f"请求参数: {req_body_other}")
        
        # 响应示例
        res_body = api.get("res_body", "")
        if res_body:
            parts.append(f"响应示例: {res_body}")
        
        return "\n".join(parts)
