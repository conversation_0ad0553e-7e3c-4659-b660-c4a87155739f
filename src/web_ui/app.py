"""
FastAPI Web应用
"""

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from contextlib import asynccontextmanager
from loguru import logger

from .routers import admin_router, app_router, chat_router, config_router, search_router
from ..config.logger import setup_logger


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("RestAgent应用启动")
    yield
    # 关闭时执行
    logger.info("RestAgent应用关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    
    app = FastAPI(
        title="RestAgent",
        description="调用REST API工具的智能体应用",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # 挂载静态文件
    app.mount("/static", StaticFiles(directory="static"), name="static")
    
    # 配置模板
    templates = Jinja2Templates(directory="templates")
    
    # 注册路由
    app.include_router(chat_router.router, prefix="/api/chat", tags=["聊天"])
    app.include_router(search_router.router, prefix="/api/search", tags=["搜索"])
    app.include_router(config_router.router, prefix="/api/config", tags=["配置"])
    app.include_router(admin_router.router, prefix="/api/admin", tags=["管理"])
    app.include_router(app_router.router, prefix="/apps", tags=["个性化APP"])
    
    @app.get("/", response_class=HTMLResponse)
    async def index(request: Request):
        """首页"""
        return templates.TemplateResponse("index.html", {"request": request})
    
    @app.get("/search", response_class=HTMLResponse)
    async def search_page(request: Request):
        """搜索页面"""
        return templates.TemplateResponse("search.html", {"request": request})
    
    @app.get("/admin", response_class=HTMLResponse)
    async def admin_page(request: Request):
        """管理页面"""
        return templates.TemplateResponse("admin.html", {"request": request})
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {"status": "healthy", "message": "RestAgent is running"}
    
    return app
