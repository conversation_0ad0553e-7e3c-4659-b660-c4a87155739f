"""
配置路由
"""

from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from loguru import logger

from ...config.settings import get_settings

router = APIRouter()


class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    section: str
    config: Dict[str, Any]


@router.get("/")
async def get_config():
    """获取当前配置"""
    try:
        settings = get_settings()
        
        # 返回配置信息（隐藏敏感信息）
        config = {
            "yapi": {
                "base_url": settings.yapi.base_url,
                "username": settings.yapi.username,
                "project_id": settings.yapi.project_id,
                "password": "***" if settings.yapi.password else ""
            },
            "llm": {
                "provider": settings.llm.provider,
                "base_url": settings.llm.base_url(),
                "model_name": settings.llm.model_name(),
                "temperature": settings.llm.temperature,
                "max_tokens": settings.llm.max_tokens,
                "api_key": "***" if settings.llm.api_key() else ""
            },
            "embedding": {
                "provider": settings.embedding.provider,
                "base_url": settings.embedding.base_url,
                "model_name": settings.embedding.model_name,
                "api_key": "***" if settings.embedding.api_key else ""
            },
            "vector_db": {
                "type": settings.vector_db.type,
                "persist_directory": settings.vector_db.persist_directory,
                "collection_name": settings.vector_db.collection_name
            },
            "web": {
                "host": settings.web.host,
                "port": settings.web.port,
                "debug": settings.web.debug
            },
            "logging": {
                "level": settings.logging.level,
                "file": settings.logging.file
            }
        }
        
        return {
            "success": True,
            "config": config
        }
        
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/update")
async def update_config(request: ConfigUpdateRequest):
    """更新配置"""
    try:
        logger.info(f"更新配置: {request.section}")
        
        # 这里应该实现配置更新逻辑
        # 由于配置是从文件加载的，需要写回文件
        # 暂时返回成功响应
        
        return {
            "success": True,
            "message": f"配置 {request.section} 更新成功",
            "section": request.section,
            "config": request.config
        }
        
    except Exception as e:
        logger.error(f"更新配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test/yapi")
async def test_yapi_connection():
    """测试YApi连接"""
    try:
        logger.info("测试YApi连接")
        
        from ...yapi_client import YApiManager
        yapi_manager = YApiManager()
        
        # 获取项目信息来测试连接
        project_info = yapi_manager.get_project_info()
        
        if project_info:
            return {
                "success": True,
                "message": "YApi连接成功",
                "project": {
                    "name": project_info.get("name", ""),
                    "desc": project_info.get("desc", ""),
                    "id": project_info.get("_id", "")
                }
            }
        else:
            return {
                "success": False,
                "message": "YApi连接失败，请检查配置"
            }
        
    except Exception as e:
        logger.error(f"测试YApi连接失败: {e}")
        return {
            "success": False,
            "message": f"YApi连接测试失败: {str(e)}"
        }


@router.post("/test/llm")
async def test_llm_connection():
    """测试大模型连接"""
    try:
        logger.info("测试大模型连接")
        
        from ...agent.llm_client import LLMClient
        llm_client = LLMClient()
        
        # 发送测试消息
        test_messages = [
            {"role": "user", "content": "Hello, this is a test message."}
        ]
        
        response = llm_client.chat_completion(test_messages)
        
        if "error" not in response and response.get("choices"):
            return {
                "success": True,
                "message": "大模型连接成功",
                "model": response.get("model", ""),
                "response": response["choices"][0]["message"]["content"][:100] + "..."
            }
        else:
            return {
                "success": False,
                "message": f"大模型连接失败: {response.get('error', '未知错误')}"
            }
        
    except Exception as e:
        logger.error(f"测试大模型连接失败: {e}")
        return {
            "success": False,
            "message": f"大模型连接测试失败: {str(e)}"
        }


@router.post("/test/vector_db")
async def test_vector_db_connection():
    """测试向量数据库连接"""
    try:
        logger.info("测试向量数据库连接")
        
        from ...vector_db import VectorDBManager
        vector_db = VectorDBManager()
        
        # 获取数据库信息
        db_info = vector_db.get_database_info()
        
        if db_info.get("status") == "connected":
            return {
                "success": True,
                "message": "向量数据库连接成功",
                "info": db_info
            }
        else:
            return {
                "success": False,
                "message": f"向量数据库连接失败: {db_info.get('error', '未知错误')}"
            }
        
    except Exception as e:
        logger.error(f"测试向量数据库连接失败: {e}")
        return {
            "success": False,
            "message": f"向量数据库连接测试失败: {str(e)}"
        }
