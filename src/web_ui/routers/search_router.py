"""
API搜索路由
"""

from typing import List, Dict, Any, Optional, Union
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from loguru import logger

from ...agent import RestAgent
from ...vector_db import VectorDBManager
from ...yapi_client import YApiManager
from ...config.settings import get_settings

router = APIRouter()

# 全局实例
agent = RestAgent()
yapi_manager = YApiManager()
vector_manager = VectorDBManager()


class SearchRequest(BaseModel):
    """搜索请求"""
    query: str
    n_results: int = 10


class ApiCallRequest(BaseModel):
    """API调用请求"""
    api_id: Union[str, int]
    parameters: Dict[str, Any] = {}
    base_url: Optional[str] = None


@router.post("/vector")
async def vector_search(request: SearchRequest):
    """向量搜索API"""
    try:
        logger.info(f"向量搜索: {request.query}")
        
        results = agent.search_apis(request.query, request.n_results)
        
        return {
            "success": True,
            "query": request.query,
            "results": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"向量搜索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/keyword")
async def keyword_search(
    q: str = Query(..., description="搜索关键词"),
    project_id: Optional[str] = Query(None, description="项目ID")
):
    """关键词搜索API"""
    try:
        logger.info(f"关键词搜索: {q}")
        
        results = vector_manager.search_apis_by_keyword(q, project_id)
        
        return {
            "success": True,
            "query": q,
            "results": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"关键词搜索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/detail/{api_id}")
async def get_api_detail(api_id: str):
    """获取API详细信息"""
    try:
        logger.info(f"获取API详情: {api_id}")
        
        api_detail = yapi_manager.get_api_detail(api_id)
        
        if not api_detail:
            raise HTTPException(status_code=404, detail="API不存在")
        
        return {
            "success": True,
            "api": api_detail
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取API详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/call")
async def call_api(request: ApiCallRequest):
    """调用API"""
    try:
        logger.info(f"调用API: {request.api_id}")
        
        if not request.base_url:
            settings = get_settings()
            request.base_url = settings.rest_auth.services[request.api_id]["base_url"]
        # 获取API详细信息
        api_detail = yapi_manager.get_api_detail(request.api_id)
        if not api_detail:
            raise HTTPException(status_code=404, detail="API不存在")
        
        # 调用API
        from ...agent.api_caller import ApiCaller
        api_caller = ApiCaller()
        
        result = api_caller.call_api(
            api_data=api_detail,
            parameters=request.parameters,
            base_url=request.base_url
        )
        
        return {
            "success": True,
            "api_id": request.api_id,
            "parameters": request.parameters,
            "result": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"调用API失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/curl/{api_id}")
async def generate_curl(
    api_id: str,
    base_url: Optional[str] = Query(None, description="基础URL")
):
    """生成curl命令"""
    try:
        logger.info(f"生成curl命令: {api_id}")
        
        # 获取API详细信息
        api_detail = yapi_manager.get_api_detail(api_id)
        if not api_detail:
            raise HTTPException(status_code=404, detail="API不存在")
        
        # 生成curl命令
        curl_command = yapi_manager.generate_api_curl(api_detail, base_url or "")
        
        return {
            "success": True,
            "api_id": api_id,
            "curl": curl_command
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成curl命令失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_api_statistics(
    project_id: Optional[str] = Query(None, description="项目ID")
):
    """获取API统计信息"""
    try:
        logger.info("获取API统计信息")
        
        stats = vector_manager.get_project_statistics(project_id)
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"获取API统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
