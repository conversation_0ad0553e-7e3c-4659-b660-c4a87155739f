"""
管理路由
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from loguru import logger

from ...agent import RestAgent
from ...vector_db import VectorDBManager

router = APIRouter()

# 全局Agent实例
agent = RestAgent()
vectorManager = VectorDBManager()


class SyncRequest(BaseModel):
    """同步请求"""
    project_id: Optional[str] = None


@router.post("/sync")
async def sync_apis(request: SyncRequest):
    """同步API数据"""
    try:
        logger.info(f"开始同步API数据, 项目ID: {request.project_id}")
        
        success = agent.sync_apis(request.project_id)
        
        if success:
            # 获取同步后的数据库信息
            db_info = agent.get_database_info()
            
            return {
                "success": True,
                "message": "API数据同步成功",
                "database_info": db_info
            }
        else:
            return {
                "success": False,
                "message": "API数据同步失败，请检查YApi配置和网络连接"
            }
        
    except Exception as e:
        logger.error(f"同步API数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/database/info")
async def get_database_info():
    """获取数据库信息"""
    try:
        db_info = agent.get_database_info()
        
        return {
            "success": True,
            "database_info": db_info
        }
        
    except Exception as e:
        logger.error(f"获取数据库信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/database/clear")
async def clear_database():
    """清空数据库"""
    try:
        logger.info("开始清空向量数据库")
        
        success = agent.clear_database()
        
        if success:
            return {
                "success": True,
                "message": "向量数据库已清空"
            }
        else:
            return {
                "success": False,
                "message": "清空向量数据库失败"
            }
        
    except Exception as e:
        logger.error(f"清空数据库失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/project/info")
async def get_project_info(
    project_id: Optional[str] = Query(None, description="项目ID")
):
    """获取项目信息"""
    try:
        from ...yapi_client import YApiManager
        yapi_manager = YApiManager()
        
        project_info = yapi_manager.get_project_info(project_id)
        
        if project_info:
            return {
                "success": True,
                "project": project_info
            }
        else:
            return {
                "success": False,
                "message": "获取项目信息失败，请检查项目ID和YApi配置"
            }
        
    except Exception as e:
        logger.error(f"获取项目信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/project/statistics")
async def get_project_statistics(
    project_id: Optional[str] = Query(None, description="项目ID")
):
    """获取项目统计信息"""
    try:        
        stats = vectorManager.get_project_statistics(project_id)
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"获取项目统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs")
async def get_logs(
    lines: int = Query(100, description="日志行数"),
    level: Optional[str] = Query(None, description="日志级别")
):
    """获取日志"""
    try:
        from ...config.settings import get_settings
        import os
        
        settings = get_settings()
        log_file = settings.logging.file
        
        if not os.path.exists(log_file):
            return {
                "success": False,
                "message": "日志文件不存在"
            }
        
        # 读取最后N行日志
        with open(log_file, 'r', encoding='utf-8') as f:
            log_lines = f.readlines()
        
        # 获取最后N行
        recent_logs = log_lines[-lines:] if len(log_lines) > lines else log_lines
        
        # 如果指定了日志级别，进行过滤
        if level:
            filtered_logs = []
            for line in recent_logs:
                if level.upper() in line:
                    filtered_logs.append(line)
            recent_logs = filtered_logs
        
        return {
            "success": True,
            "logs": recent_logs,
            "total_lines": len(log_lines),
            "returned_lines": len(recent_logs)
        }
        
    except Exception as e:
        logger.error(f"获取日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/status")
async def get_system_status():
    """获取系统状态"""
    try:
        import psutil
        import platform
        from datetime import datetime
        
        # 系统信息
        system_info = {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "disk_usage": psutil.disk_usage('/').percent,
            "uptime": datetime.now().isoformat()
        }
        
        # 数据库状态
        db_info = agent.get_database_info()
        
        return {
            "success": True,
            "system": system_info,
            "database": db_info
        }
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        # 如果psutil不可用，返回基本信息
        return {
            "success": True,
            "system": {
                "platform": "unknown",
                "status": "running"
            },
            "database": agent.get_database_info()
        }
