"""
聊天路由
"""

from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import json
from loguru import logger

from ...agent import RestAgent

router = APIRouter()

# 全局Agent实例
agent = RestAgent()


class ChatRequest(BaseModel):
    """聊天请求"""
    message: str
    history: List[Dict[str, str]] = []
    max_iterations: int = 5


class ChatResponse(BaseModel):
    """聊天响应"""
    type: str
    message: str
    data: Dict[str, Any] = {}


@router.post("/send")
async def send_message(request: ChatRequest):
    """发送消息"""
    try:
        logger.info(f"收到聊天请求: {request.message}")
        
        def generate_response():
            """生成流式响应"""
            try:
                for step in agent.chat(
                    user_input=request.message,
                    conversation_history=request.history,
                    max_iterations=request.max_iterations
                ):
                    # 转换为JSON字符串并添加换行符
                    yield f"data: {json.dumps(step, ensure_ascii=False)}\n\n"
                
                # 发送结束标记
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error(f"生成响应失败: {e}")
                error_response = {
                    "type": "error",
                    "message": f"生成响应时发生错误: {str(e)}"
                }
                yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*"
            }
        )
        
    except Exception as e:
        logger.error(f"处理聊天请求失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history")
async def get_chat_history():
    """获取聊天历史（暂时返回空列表）"""
    return {"history": []}


@router.delete("/history")
async def clear_chat_history():
    """清空聊天历史"""
    return {"message": "聊天历史已清空"}


@router.get("/status")
async def get_chat_status():
    """获取聊天状态"""
    try:
        db_info = agent.get_database_info()
        return {
            "status": "ready",
            "database": db_info
        }
    except Exception as e:
        logger.error(f"获取聊天状态失败: {e}")
        return {
            "status": "error",
            "error": str(e)
        }
