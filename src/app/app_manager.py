"""
个性化APP管理器
"""

import os
import json
import shutil
import zipfile
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from loguru import logger
from .app_builder import AppBuilder
from ..config.settings import get_settings
from ..agent.claude_agent import ClaudeA<PERSON>

from .models import (
    AppMetadata, AppPage, AppConfig, AppAccessLog, 
    AppBuildHistory, AppStats, AppExportData,
    generate_app_id, validate_app_name, validate_page_name, sanitize_html_content
)


class AppManager:
    """APP管理器"""
    
    def __init__(self, clean_up: bool = False):
        self.apps_config_path = "data/apps/config/apps.json"
        self.apps_dir = "data/html/app"
        self.logs_dir = "data/apps/logs"
        
        if clean_up:
            logger.warning("正在清理APP目录.....")
            shutil.rmtree(self.apps_dir, ignore_errors=True)
            shutil.rmtree(self.apps_config_path, ignore_errors=True)
            shutil.rmtree(self.logs_dir, ignore_errors=True)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.apps_config_path), exist_ok=True)
        os.makedirs(self.apps_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)
        
        self.settings = get_settings()
        
        # 初始化配置文件
        self._init_config()
        
        if self.settings.llm.provider == "claude":
            self.app_builder = ClaudeAgent(enable_session=True)
        else:
            self.app_builder = AppBuilder(self)
    
    def _init_config(self):
        """初始化配置文件"""
        if not os.path.exists(self.apps_config_path):
            config = {
                "apps": {},
                "metadata": {
                    "version": "1.0.0",
                    "created_at": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
            }
            self._save_config(config)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            with open(self.apps_config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载APP配置失败: {e}")
            return {"apps": {}, "metadata": {}}
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置"""
        try:
            config["metadata"]["last_updated"] = datetime.now().isoformat()
            with open(self.apps_config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存APP配置失败: {e}")
            raise
    
    def create_app(self, name: str, description: str, author: str = "用户") -> str:
        """创建新APP"""
        if not validate_app_name(name):
            raise ValueError("APP名称无效")
        
        app_id = generate_app_id()
        metadata = AppMetadata(
            id=app_id,
            name=name,
            description=description,
            author=author
        )
        
        # 创建APP目录
        app_dir = os.path.join(self.apps_dir, app_id)
        os.makedirs(app_dir, exist_ok=True)
        
        # 创建默认页面
        default_page = AppPage(
            name="index.html",
            title=name,
            content=self._get_default_page_content(name, description)
        )
        
        # 保存页面文件
        page_path = os.path.join(app_dir, "index.html")
        with open(page_path, 'w', encoding='utf-8') as f:
            f.write(default_page.content)
        
        # 保存元数据
        metadata_path = os.path.join(app_dir, "metadata.json")
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata.to_dict(), f, ensure_ascii=False, indent=2)
        
        # 更新配置
        config = self._load_config()
        config["apps"][app_id] = metadata.to_dict()
        self._save_config(config)
        
        logger.info(f"创建APP成功: {app_id} - {name}")
        return app_id
    
    def get_apps(self, category: Optional[str] = None, search: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取APP列表"""
        config = self._load_config()
        apps = list(config["apps"].values())
        
        # 分类过滤
        if category:
            apps = [app for app in apps if app.get("category") == category]
        
        # 搜索过滤
        if search:
            search_lower = search.lower()
            apps = [
                app for app in apps 
                if search_lower in app.get("name", "").lower() 
                or search_lower in app.get("description", "").lower()
                or any(search_lower in tag.lower() for tag in app.get("tags", []))
            ]
        
        # 按更新时间排序
        apps.sort(key=lambda x: x.get("updated_at", ""), reverse=True)
        return apps
    
    def get_app(self, app_id: str) -> Optional[Dict[str, Any]]:
        """获取APP详情"""
        config = self._load_config()
        return config["apps"].get(app_id)
    
    def update_app_metadata(self, app_id: str, **kwargs) -> bool:
        """更新APP元数据"""
        config = self._load_config()
        if app_id not in config["apps"]:
            return False
        
        app_data = config["apps"][app_id]
        
        # 更新字段
        for key, value in kwargs.items():
            if key in ["name", "description", "icon", "category", "tags", "author", "version", "is_public"]:
                app_data[key] = value
        
        app_data["updated_at"] = datetime.now().isoformat()
        
        # 保存到文件
        app_dir = os.path.join(self.apps_dir, app_id)
        metadata_path = os.path.join(app_dir, "metadata.json")
        if os.path.exists(metadata_path):
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(app_data, f, ensure_ascii=False, indent=2)
        
        self._save_config(config)
        return True
    def get_app_dir(self, app_id: str, mkdir:bool = True) -> str:
        """获取APP页面内容"""        
        page_path = os.path.join(self.apps_dir, app_id)
        if not os.path.exists(page_path) and mkdir:
            # 创建应用目录
            os.makedirs(page_path)
        return page_path
    
    def get_app_page(self, app_id: str, page_name: str = "index.html") -> Optional[str]:
        """获取APP页面内容"""
        if not validate_page_name(page_name):
            return None
        
        page_path = os.path.join(self.apps_dir, app_id, page_name)
        if not os.path.exists(page_path):
            return None
        
        try:
            with open(page_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取APP页面失败: {e}")
            return None
    
    def update_app_content(self, app_id: str, page_name: str, content: str) -> bool:
        """更新APP页面内容"""
        if not validate_page_name(page_name):
            return False
        
        app_dir = os.path.join(self.apps_dir, app_id)
        if not os.path.exists(app_dir):
            return False
        
        # 清理HTML内容
        content = sanitize_html_content(content)
        
        # 保存页面
        page_path = os.path.join(app_dir, page_name)
        try:
            with open(page_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 更新元数据中的页面列表
            config = self._load_config()
            if app_id in config["apps"]:
                pages = config["apps"][app_id].get("pages", [])
                if page_name not in pages:
                    pages.append(page_name)
                    config["apps"][app_id]["pages"] = pages
                
                config["apps"][app_id]["updated_at"] = datetime.now().isoformat()
                self._save_config(config)
            
            return True
        except Exception as e:
            logger.error(f"更新APP内容失败: {e}")
            return False
    
    def delete_app(self, app_id: str) -> bool:
        """删除APP"""
        config = self._load_config()
        if app_id not in config["apps"]:
            return False
        
        # 删除APP目录
        app_dir = os.path.join(self.apps_dir, app_id)
        if os.path.exists(app_dir):
            shutil.rmtree(app_dir)
        
        # 从配置中移除
        del config["apps"][app_id]
        self._save_config(config)
        
        logger.info(f"删除APP成功: {app_id}")
        return True
    
    def log_access(self, app_id: str, ip_address: str, user_agent: str = "", page: str = "index.html"):
        """记录访问日志"""
        log_entry = AppAccessLog(
            app_id=app_id,
            ip_address=ip_address,
            user_agent=user_agent,
            page=page
        )
        
        # 保存到日志文件
        log_file = os.path.join(self.logs_dir, f"{app_id}_access.log")
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry.to_dict(), ensure_ascii=False) + '\n')
            
            # 更新访问计数
            config = self._load_config()
            if app_id in config["apps"]:
                config["apps"][app_id]["access_count"] = config["apps"][app_id].get("access_count", 0) + 1
                self._save_config(config)
                
        except Exception as e:
            logger.error(f"记录访问日志失败: {e}")
    
    def get_app_logs(self, app_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取APP访问日志"""
        log_file = os.path.join(self.logs_dir, f"{app_id}_access.log")
        if not os.path.exists(log_file):
            return []
        
        logs = []
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 获取最后limit条记录
                for line in lines[-limit:]:
                    if line.strip():
                        logs.append(json.loads(line.strip()))
        except Exception as e:
            logger.error(f"读取访问日志失败: {e}")
        
        return logs

    def export_app(self, app_id: str) -> Optional[str]:
        """导出APP为ZIP文件"""
        app_dir = os.path.join(self.apps_dir, app_id)
        if not os.path.exists(app_dir):
            return None

        # 创建临时导出目录
        export_dir = os.path.join("temp", f"export_{app_id}")
        os.makedirs(export_dir, exist_ok=True)

        try:
            # 复制APP文件
            shutil.copytree(app_dir, os.path.join(export_dir, app_id))

            # 创建ZIP文件
            zip_path = os.path.join("temp", f"{app_id}.zip")
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(export_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, export_dir)
                        zipf.write(file_path, arcname)

            # 清理临时目录
            shutil.rmtree(export_dir)

            return zip_path

        except Exception as e:
            logger.error(f"导出APP失败: {e}")
            if os.path.exists(export_dir):
                shutil.rmtree(export_dir)
            return None

    def import_app(self, zip_path: str) -> Optional[str]:
        """从ZIP文件导入APP"""
        if not os.path.exists(zip_path):
            return None

        # 创建临时导入目录
        import_dir = os.path.join("temp", f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(import_dir, exist_ok=True)

        try:
            # 解压ZIP文件
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                zipf.extractall(import_dir)

            # 查找APP目录
            app_dirs = [d for d in os.listdir(import_dir) if os.path.isdir(os.path.join(import_dir, d))]
            if not app_dirs:
                raise ValueError("ZIP文件中未找到APP目录")

            old_app_id = app_dirs[0]
            old_app_dir = os.path.join(import_dir, old_app_id)

            # 读取元数据
            metadata_path = os.path.join(old_app_dir, "metadata.json")
            if not os.path.exists(metadata_path):
                raise ValueError("未找到APP元数据文件")

            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata_data = json.load(f)

            # 生成新的APP ID
            new_app_id = generate_app_id()
            metadata_data["id"] = new_app_id
            metadata_data["name"] = f"{metadata_data['name']}_导入"
            metadata_data["created_at"] = datetime.now().isoformat()
            metadata_data["updated_at"] = datetime.now().isoformat()

            # 创建新的APP目录
            new_app_dir = os.path.join(self.apps_dir, new_app_id)
            shutil.copytree(old_app_dir, new_app_dir)

            # 更新元数据文件
            new_metadata_path = os.path.join(new_app_dir, "metadata.json")
            with open(new_metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata_data, f, ensure_ascii=False, indent=2)

            # 更新配置
            config = self._load_config()
            config["apps"][new_app_id] = metadata_data
            self._save_config(config)

            # 清理临时目录
            shutil.rmtree(import_dir)

            logger.info(f"导入APP成功: {new_app_id}")
            return new_app_id

        except Exception as e:
            logger.error(f"导入APP失败: {e}")
            if os.path.exists(import_dir):
                shutil.rmtree(import_dir)
            return None

    def _get_default_page_content(self, name: str, description: str) -> str:
        """获取默认页面内容"""
        return ""
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{name}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h1 class="card-title mb-0">
                            <i class="fas fa-cube me-2"></i>{name}
                        </h1>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{description}</p>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            这是一个由AI生成的个性化APP。您可以通过APP构建器进一步定制和完善功能。
                        </div>
                        <div class="text-center">
                            <button class="btn btn-primary" onclick="alert('欢迎使用个性化APP！')">
                                <i class="fas fa-rocket me-2"></i>开始使用
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>"""
    
    async def build_app(self, session_id: str,
        app_id: Optional[str],
        name: str,
        description: str,
        requirements: str,
        iteration_history: Optional[List[Dict[str, Any]]] = None,
        stream_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        构建APP

        Args:
            app_id: APP ID（如果是更新现有APP）
            name: APP名称
            description: APP描述
            requirements: 用户需求
            iteration_history: 迭代历史
            stream_callback: 流式回调函数

        Returns:
            Dict: 构建结果
        """
        if not validate_app_name(name):
            raise ValueError("APP名称无效")
        # 如果是新APP，创建APP ID
        if not app_id:
            app_id = self.create_app(name, description)
            is_new_app = True
        else:
            is_new_app = False
            # 验证APP是否存在
            existing_app = self.get_app(app_id)
            if not existing_app:
                raise ValueError("APP不存在")        
            
        app_dir = self.get_app_dir(app_id)
        result = await self.app_builder.build_app(session_id = session_id,
                    app_id=app_id,
                    app_dir=app_dir,
                    is_new_app = is_new_app,
                    name=name,
                    description=description,
                    requirements=requirements,
                    iteration_history=iteration_history,
                    stream_callback=stream_callback)
        return result