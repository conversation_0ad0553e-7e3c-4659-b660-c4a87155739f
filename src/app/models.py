"""
个性化APP数据模型
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import uuid
import json


class AppPage(BaseModel):
    """APP页面模型"""
    name: str = Field(..., description="页面名称")
    title: str = Field(..., description="页面标题")
    content: str = Field(..., description="页面HTML内容")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())


class AppMetadata(BaseModel):
    """APP元数据模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(..., description="APP名称")
    description: str = Field(..., description="APP描述")
    icon: str = Field(default="fas fa-cube", description="APP图标")
    category: str = Field(default="其他", description="APP分类")
    tags: List[str] = Field(default_factory=list, description="APP标签")
    author: str = Field(default="用户", description="作者")
    version: str = Field(default="1.0.0", description="版本号")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    is_public: bool = Field(default=True, description="是否公开")
    access_count: int = Field(default=0, description="访问次数")
    pages: List[str] = Field(default_factory=lambda: ["index.html"], description="页面列表")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AppMetadata":
        """从字典创建"""
        return cls(**data)


class AppConfig(BaseModel):
    """APP配置模型"""
    app_id: str = Field(..., description="APP ID")
    settings: Dict[str, Any] = Field(default_factory=dict, description="配置设置")
    api_endpoints: List[Dict[str, Any]] = Field(default_factory=list, description="API端点配置")
    dependencies: List[str] = Field(default_factory=list, description="依赖库")
    environment: Dict[str, str] = Field(default_factory=dict, description="环境变量")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AppConfig":
        """从字典创建"""
        return cls(**data)


class AppAccessLog(BaseModel):
    """APP访问日志模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    app_id: str = Field(..., description="APP ID")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    ip_address: str = Field(..., description="访问IP")
    user_agent: str = Field(default="", description="用户代理")
    page: str = Field(default="index.html", description="访问页面")
    referrer: str = Field(default="", description="来源页面")
    session_id: str = Field(default="", description="会话ID")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AppAccessLog":
        """从字典创建"""
        return cls(**data)


class AppBuildHistory(BaseModel):
    """APP构建历史模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    app_id: str = Field(..., description="APP ID")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    user_request: str = Field(..., description="用户请求")
    ai_response: str = Field(..., description="AI响应")
    generated_content: str = Field(..., description="生成的内容")
    iteration_number: int = Field(..., description="迭代次数")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AppBuildHistory":
        """从字典创建"""
        return cls(**data)


class AppStats(BaseModel):
    """APP统计信息模型"""
    app_id: str = Field(..., description="APP ID")
    total_visits: int = Field(default=0, description="总访问量")
    unique_visitors: int = Field(default=0, description="独立访客数")
    daily_visits: Dict[str, int] = Field(default_factory=dict, description="每日访问量")
    popular_pages: Dict[str, int] = Field(default_factory=dict, description="热门页面")
    last_updated: str = Field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AppStats":
        """从字典创建"""
        return cls(**data)


class AppExportData(BaseModel):
    """APP导出数据模型"""
    metadata: AppMetadata
    pages: List[AppPage]
    config: AppConfig
    build_history: List[AppBuildHistory]
    export_timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    export_version: str = Field(default="1.0.0")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.model_dump()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AppExportData":
        """从字典创建"""
        return cls(**data)


# 工具函数
def generate_app_id() -> str:
    """生成APP ID"""
    return str(uuid.uuid4())


def validate_app_name(name: str) -> bool:
    """验证APP名称"""
    if not name or len(name.strip()) == 0:
        return False
    if len(name) > 100:
        return False
    # 检查是否包含非法字符
    invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
    return not any(char in name for char in invalid_chars)


def validate_page_name(page_name: str) -> bool:
    """验证页面名称"""
    if not page_name or not page_name.endswith('.html'):
        return False
    if len(page_name) > 50:
        return False
    # 检查是否包含非法字符
    invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
    return not any(char in page_name for char in invalid_chars)


def sanitize_html_content(content: str) -> str:
    """清理HTML内容"""
    # 基本的HTML清理，移除潜在的危险标签
    import re
    
    # 移除script标签
    content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL | re.IGNORECASE)
    
    # 移除危险的事件处理器
    dangerous_attrs = ['onload', 'onclick', 'onmouseover', 'onerror', 'onsubmit']
    for attr in dangerous_attrs:
        content = re.sub(f'{attr}\\s*=\\s*["\'][^"\']*["\']', '', content, flags=re.IGNORECASE)
    
    return content
