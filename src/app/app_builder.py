"""
个性化APP构建器
"""

import json
import os
import asyncio
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from loguru import logger

from .models import AppMetadata, AppBuildHistory, generate_app_id, validate_app_name, sanitize_html_content
#from .app_manager import AppManager
from ..agent.llm_client import LLMClient
from ..vector_db.vector_manager import VectorDBManager
from ..agent.llm_agent import LLMAgent
from ..agent.tool_manager import ToolManager,Tool

class SearchApi(Tool):    
    """查询API接口的工具"""
    
    def __init__(self, vector_db: VectorDBManager):
        self.vector_db = vector_db
    @property
    def name(self) -> str:
        return "search_api"
    
    @property
    def description(self) -> str:
        return "根据用户的需求，检索相关的REST API接口信息"
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "keyword": {
                    "type": "string",
                    "description": "用户的功能需求"
                }
            },
            "required": ["keyword"]
        }
    
    async def execute(self, parameters: Any) -> str:
        try:
            # 检查parameters类型，确保类型安全
            if isinstance(parameters, dict):
                query = parameters.get("keyword", "")
            else:
                query = str(parameters)
            apis = self.vector_db.search_apis(query, 5)
            result = "\n".join(f"{api}" for api in apis)
            return result
        except Exception as e:
            logger.error(f"搜索API失败: {e}")
            return ""
    

class AppBuilder:
    """APP构建器"""

    def __init__(self, app_manager):
        self.app_manager = app_manager
        self.llm_client = LLMClient()
        self.prompts_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "prompts")
        self.vector_db = VectorDBManager()
        
        self.tool_manager = ToolManager()
        self.tool_manager.add_tool(SearchApi(self.vector_db))
        self.code_agent = LLMAgent(enable_session=True, tool_manager=self.tool_manager)
        
    async def build_app(self, session_id: str,
        app_id: Optional[str],
        is_new_app : bool,
        app_dir: str,
        name: str,
        description: str,
        requirements: str,
        iteration_history: Optional[List[Dict[str, Any]]] = None,
        stream_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        构建APP

        Args:
            app_id: APP ID（如果是更新现有APP）
            name: APP名称
            description: APP描述
            requirements: 用户需求
            iteration_history: 迭代历史
            stream_callback: 流式回调函数

        Returns:
            Dict: 构建结果
        """
        try:
            if not validate_app_name(name):
                raise ValueError("APP名称无效")
            
            # 构建系统提示
            system_prompt = self._build_system_prompt()
            
            # 构建用户消息
            user_message = requirements
            
            response = await self.code_agent.process_message(session_id, system_prompt, user_message, stream_callback)
            
            if isinstance(response, dict) and "error" in response:
                error_msg = response.get("error", "未知错误")
                raise Exception(f"UI代码生成失败: {error_msg}")
            
            # 提取生成的HTML内容
            if isinstance(response, dict):
                generated_content = response.get("content", "")
            else:
                generated_content = str(response)
                
            if not generated_content:
                raise Exception("智能体未返回有效内容")
            
            # 清理和验证HTML内容
            html_content = self._extract_html_content(generated_content)
            #html_content = sanitize_html_content(html_content)
            
            # 保存APP内容
            success = self.app_manager.update_app_content(
                app_id=app_id,
                page_name="index.html",
                content=html_content
            )
            
            if not success:
                raise Exception("保存APP内容失败")
            
            # 记录构建历史
            build_history = AppBuildHistory(
                app_id=app_id,
                user_request=requirements,
                ai_response=generated_content,
                generated_content=html_content,
                iteration_number=len(iteration_history) + 1 if iteration_history else 1
            )
            
            self._save_build_history(build_history)
            
            # 更新APP元数据
            if not is_new_app:
                self.app_manager.update_app_metadata(
                    app_id=app_id,
                    name=name,
                    description=description,
                    updated_at=datetime.now().isoformat()
                )
            
            return {
                "app_id": app_id,
                "content": html_content,
                "message": "APP构建成功" if is_new_app else "APP更新成功"
            }
            
        except Exception as e:
            raise Exception(f"构建APP失败: {e}")
    
    def _load_prompt_template(self, template_name: str) -> str:
        """加载提示词模板"""
        try:
            template_path = os.path.join(self.prompts_dir, f"{template_name}.md")
            if os.path.exists(template_path):
                with open(template_path, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                logger.warning(f"提示词模板文件不存在: {template_path}")
                return ""
        except Exception as e:
            logger.error(f"加载提示词模板失败: {e}")
            return ""
    
    def _build_system_prompt(self) -> str:
        """构建系统提示"""
        # 加载系统提示词模板
        system_template = self._load_prompt_template("app_builder_system")

        # 如果模板加载失败，抛出异常
        if not system_template:
            raise Exception("APP构建的系统提示词模板加载失败")

        return system_template
    
    def _build_user_message(
        self,
        name: str,
        description: str,
        requirements: str,
        iteration_history: Optional[List[Dict[str, Any]]]
    ) -> str:
        """构建用户消息"""
        # 加载用户提示词模板
        user_template = self._load_prompt_template("app_builder_user")

        # 如果模板加载失败，抛出异常
        if not user_template:
            raise Exception("APP构建模板加载失败")

        # 构建迭代说明
        iteration_note = ""
        if iteration_history:
            iteration_note = "\n**注意**：这是一个迭代更新，之前的版本已经存在。请根据新的需求对现有功能进行改进和完善。\n"
        
        # iteration_note中有\n，模态替换会失败
        user_template = user_template.replace("{iteration_note}", str(iteration_note))
        
        # 代理请求格式
        request_format = """
          ```javascript
  POST /apps/api/proxy
  Content-Type: application/json
  
  {
    "url": "实际API地址",
    "method": "GET/POST/PUT/DELETE", 
    "headers": {},
    "data": {}
  }
  ```
        """

        # 替换模板变量
        try:
            message = user_template.format(
                name=name,
                description=description,
                requirements=requirements,
                iteration_note=iteration_note,
                request_format=request_format,
                api_list=""  # API列表在系统提示词中已包含
            )
        except (KeyError, ValueError) as e:
            raise Exception(f"模板变量替换失败: {e}")

        return message
    
    def _extract_html_content(self, content: str) -> str:
        """提取HTML内容"""
        # 查找HTML代码块
        import re
        
        # 尝试提取```html代码块
        html_match = re.search(r'```html\s*(.*?)\s*```', content, re.DOTALL | re.IGNORECASE)
        if html_match:
            return html_match.group(1).strip()
        
        # 尝试提取```代码块
        code_match = re.search(r'```\s*(.*?)\s*```', content, re.DOTALL)
        if code_match:
            code_content = code_match.group(1).strip()
            # 检查是否是HTML内容
            if '<!DOCTYPE html>' in code_content or '<html' in code_content:
                return code_content
        
        # 如果没有代码块，查找HTML文档
        if '<!DOCTYPE html>' in content or '<html' in content:
            # 提取从<!DOCTYPE html>或<html>开始到</html>结束的内容
            start_match = re.search(r'(<!DOCTYPE html>|<html[^>]*>)', content, re.IGNORECASE)
            if start_match:
                start_pos = start_match.start()
                end_match = re.search(r'</html>', content[start_pos:], re.IGNORECASE)
                if end_match:
                    end_pos = start_pos + end_match.end()
                    return content[start_pos:end_pos]
        
        # 如果都没找到，返回原内容（可能需要进一步处理）
        return content
    
    def _save_build_history(self, build_history: AppBuildHistory):
        """保存构建历史"""
        try:
            history_file = f"data/apps/logs/{build_history.app_id}_build_history.log"
            with open(history_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(build_history.to_dict(), ensure_ascii=False) + '\n')
        except Exception as e:
            logger.error(f"保存构建历史失败: {e}")
    
    def get_build_history(self, app_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取构建历史"""
        try:
            history_file = f"data/apps/logs/{app_id}_build_history.log"
            if not os.path.exists(history_file):
                return []
            
            histories = []
            with open(history_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[-limit:]:
                    if line.strip():
                        histories.append(json.loads(line.strip()))
            
            return histories
        except Exception as e:
            logger.error(f"获取构建历史失败: {e}")
            return []
