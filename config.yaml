# RestAgent 配置文件

# YApi 配置
yapi:
  base_url: "http://************:3000/"  # YApi服务地址
  username: "<EMAIL>"  # YApi用户名
  password: "jiadx@2025"  # YApi密码
  project_id: "46"  # 项目ID

# 大模型配置
llm:
  provider: "claude"
  # base_url: "https://ai.secsign.online:3003/apiconvert/v1"
  # api_key: "sk-ToO5y0IzuYy0lTmqY1K9IKyzqGph1DhmqVwC3W0FXaoYRjIU"
  # model_name: "qwen3-32b"
  temperature: 0.5
  max_tokens: 20000
openai:
  base_url: "https://ai.secsign.online:3003/apiconvert/v1"
  api_key: "sk-ToO5y0IzuYy0lTmqY1K9IKyzqGph1DhmqVwC3W0FXaoYRjIU"
  model_name: "qwen3-32b"
dashscope:
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  api_key: "sk-1d06e75d7fd94338b5b32cf8f9099651"
  model_name: "qwen3-coder-480b-a35b-instruct"
openrouter:
  base_url: "https://openrouter.ai/api/v1"
  api_key: "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"
  model_name: "qwen/qwen3-coder:free"
claude:
  base_url: "http://10.10.43.64:8101/"
  #base_url: "http://127.0.0.1:8100/"
  api_key: "sk-ToO5y0IzuYy0lTmqY1K9IKyzqGph1DhmqVwC3W0FXaoYRjIU"
  model_name: "qwen3-32b"
  opus_model: "qwen3-32b"
  sonnect_model: "qwen_coder"
  small_model: "qwen3-32b"

# 嵌入模型配置
embedding:
  provider: "openai"
  base_url: "https://ai.secsign.online:38080"
  api_key: "sk-ToO5y0IzuYy0lTmqY1K9IKyzqGph1DhmqVwC3W0FXaoYRjIU"
  model_name: "embed"

# 向量数据库配置
vector_db:
  type: "chromadb"
  persist_directory: "./data/chroma_db"
  collection_name: "rest_apis"

# REST服务授权配置
rest_auth:
  default_headers:
    "Content-Type": "application/json"
  # 可以配置多个服务的授权信息
  services:
    "46":
      base_url: "https://************:18081/crm"
      headers:
        "Authorization": "Bearer your_token_here"

# Web UI配置
web:
  host: "0.0.0.0"
  port: 8000
  debug: true

# 日志配置
logging:
  level: "INFO"
  file: "./logs/app.log"

# 代码生成的配置
app_gen:
  allowed_tools: "bash,file_operation"  # 允许的工具
  auto_compact: false
  compact_threshold: 20
