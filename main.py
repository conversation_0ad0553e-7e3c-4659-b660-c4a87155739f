#!/usr/bin/env python3
"""
RestAgent - 调用REST API工具的智能体应用
主入口文件
"""

import uvicorn
from src.web_ui.app import create_app
from src.config.settings import get_settings

def main():
    """启动应用"""
    settings = get_settings()
    app = create_app()
    
    uvicorn.run(
        "src.web_ui.app:create_app",
        host=settings.web.host,
        port=settings.web.port,
        reload=settings.web.debug,
        factory=True
    )

if __name__ == "__main__":
    main()
