#!/usr/bin/env python3
"""
MCP服务器 - API搜索服务
实现SearchApi工具的MCP版本，支持基于向量数据库的API语义搜索功能

如果MCP库未安装，将运行为独立的JSON-RPC服务器
"""

import asyncio
import json
import logging
import sys
import os
import uvicorn
from typing import Any, Dict, List, Optional, Sequence
from fastmcp import FastMCP
# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
print("project_root:", project_root)
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from src.vector_db.vector_manager import VectorDBManager

vector_db = VectorDBManager()
mcp = FastMCP("Search API Server")

@mcp.tool()
def search_api(keywords:str) -> str:
    """
    根据用户的请求从知识库搜索相关的REST API
    Parameters:
        keywords: 功能或需求的描述
    Returns:
        符合条件的REST API的详细定义，多个REST API以\n\n分隔
    """
    apis = vector_db.search_apis(keywords, 5)
    result = "\n\n".join(f"{api}" for api in apis)
    print(f"根据[{keywords}] 查询到{len(apis)}条结果：{result[:50]}")
    return result

if __name__ == "__main__":
    # 支持多种传输模式
    # STDIO模式（用于本地Claude Desktop等）
    #mcp.run(transport="stdio")
    
    # SSE模式（用于Web浏览器访问，支持远程连接）
    # 创建FastAPI应用并挂载SSE服务器
    mcp.run(transport="sse", host="0.0.0.0", port=7000)
    # app = FastAPI(title="MCP Search API Server")
    
    # # 健康检查端点
    # @app.get("/")
    # def health_check():
    #     return {"status": "ok", "service": "MCP Search API Server"}
    
    # # 挂载SSE服务器
    # app.mount("/", create_sse_server(mcp))
    
    # # 启动服务器，指定监听地址和端口
    # uvicorn.run(
    #     app,
    #     host='0.0.0.0',  # 监听地址：127.0.0.1（本地）或 0.0.0.0（所有接口）
    #     port=7000,         # 监听端口
    #     log_level="info"
    # )