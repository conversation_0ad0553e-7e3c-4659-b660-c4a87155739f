"""
APP构建器集成测试 - 使用真实的大模型调用和配置
"""

import time
import pytest
import asyncio
import os
import uuid
import shutil
import json
import sys
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.app.app_manager import AppManager
from src.config.settings import Settings


class TestAppBuilderIntegration:
    """APP构建器集成测试 - 真实环境测试"""
    
    def __init__(self):
        # 创建APP构建器实例
        self.app_builder = AppManager(True)
        
        # 清理app数据
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_customer_app_build(self):
        """测试真实环境下构建客户档案管理APP"""
        try:
            # 测试数据
            app_id = "7b72808b-af96-4060-a861-0ed473d7d35f"
            app_name = "客户档案管理系统"
            app_description = "完整的客户信息管理平台，支持客户增删改查"
            user_requirements = """
            请帮我实现一个客户档案管理功能，要求包括：
            1. 客户列表展示，支持分页查看
            2. 新增客户功能，包含姓名、电话、邮箱、地址等基本信息
            3. 编辑客户信息功能
            4. 删除客户功能，需要确认提示
            5. 客户搜索功能
            """
            
            print(f"开始测试APP构建...")
            print(f"APP名称: {app_name}")
            print(f"需求描述: {user_requirements[:100]}...")
            
            # 执行实际的APP构建
            result = await self.app_builder.build_app(
                session_id=app_id,
                app_id=app_id,
                name=app_name,
                description=app_description,
                requirements=user_requirements
            )
            
            # 验证结果结构
            assert result is not None, "构建结果不能为空"
            assert isinstance(result, dict), "构建结果应该是字典类型"
            assert "app_id" in result, "结果中应包含app_id"
            assert "content" in result, "结果中应包含content"
            assert "message" in result, "结果中应包含message"
            
            print(f"✓ APP构建成功")
            print(f"  - APP ID: {result['app_id']}")
            print(f"  - 消息: {result['message']}")
            
            # 验证HTML内容
            html_content = result["content"]
            assert isinstance(html_content, str), "HTML内容应该是字符串"
            assert len(html_content) > 0, "HTML内容不能为空"
            
            # 验证HTML基本结构
            assert "<!DOCTYPE html>" in html_content or "<html" in html_content, "应包含HTML文档声明"
            assert "客户" in html_content, "应包含客户相关内容"
            
            print(f"✓ HTML内容验证通过")
            print(f"  - 内容长度: {len(html_content)} 字符")
            
            # 验证是否包含预期的功能元素
            expected_elements = [
                ("客户列表", "客户列表展示"),
                ("新增", "新增客户功能"),
                ("编辑", "编辑功能"),
                ("删除", "删除功能"),
                ("搜索", "搜索功能"),
                ("<script>","javascript脚本")
            ]
            
            found_elements = []
            for element, description in expected_elements:
                if element in html_content:
                    found_elements.append(description)
                    print(f"  ✓ 发现: {description}")
                else:
                    print(f"  - 未发现: {description}")
            
            # 至少应该包含客户管理的基本元素
            expected_func = len(expected_elements)
            assert len(found_elements) >= expected_func, f"应该包含至少{expected_func}个基本功能元素，实际发现: {found_elements}"
            
            # 保存生成的HTML到临时文件进行检查
            # temp_html_file = os.path.join(tempfile.gettempdir(), f"generated_app_{result['app_id']}.html")
            # with open(temp_html_file, 'w', encoding='utf-8') as f:
            #     f.write(html_content)
            # print(f"✓ 生成的HTML已保存到: {temp_html_file}")
            
            return result
            
        except Exception as e:
            print(f"✗ 集成测试失败: {e}")
            import traceback
            traceback.print_exc()
            #pytest.fail(f"集成测试失败: {e}")

# 运行特定的集成测试
if __name__ == "__main__":
    import sys
    
    # 添加项目路径
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # 运行端到端测试
    async def run_integration_test():
        print("运行集成测试...")
        test_instance = TestAppBuilderIntegration()
        
        # 创建临时目录
        import tempfile
        temp_dir = tempfile.mkdtemp(prefix="restAgent_manual_test_")
        
        try:            
            # 运行端到端测试
            await test_instance.test_customer_app_build()
            
        finally:
            # 清理
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    # 运行测试
    asyncio.run(run_integration_test())