"""
向量数据库模块测试
"""

import pytest
import tempfile
import shutil
from unittest.mock import Mock, patch
from src.vector_db.vector_manager import VectorDBManager


class TestVectorDBManager:
    """向量数据库管理器测试"""
    
    @pytest.fixture
    def temp_db_dir(self):
        """创建临时数据库目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_apis(self):
        """示例API数据"""
        return [
            {
                "_id": "1",
                "title": "获取用户信息",
                "path": "/api/user/{id}",
                "method": "GET",
                "project_id": "123",
                "catname": "用户管理",
                "desc": "根据用户ID获取用户详细信息",
                "tag": ["用户", "查询"]
            },
            {
                "_id": "2",
                "title": "创建订单",
                "path": "/api/order",
                "method": "POST",
                "project_id": "123",
                "catname": "订单管理",
                "desc": "创建新的订单",
                "tag": ["订单", "创建"]
            }
        ]
    
    @patch('src.vector_db.manager.ChromaDBClient')
    def test_add_apis(self, mock_chroma_client, sample_apis):
        """测试添加API"""
        # Mock ChromaDB客户端
        mock_client_instance = Mock()
        mock_client_instance.add_api_documents.return_value = True
        mock_chroma_client.return_value = mock_client_instance
        
        # Mock 嵌入客户端
        with patch('src.vector_db.manager.EmbeddingClient'):
            manager = VectorDBManager()
            result = manager.add_apis(sample_apis)
            
            assert result is True
            mock_client_instance.add_api_documents.assert_called_once_with(sample_apis)
    
    @patch('src.vector_db.manager.ChromaDBClient')
    def test_search_apis(self, mock_chroma_client):
        """测试搜索API"""
        # Mock搜索结果
        mock_search_results = [
            {
                "metadata": {
                    "api_id": "1",
                    "title": "获取用户信息",
                    "path": "/api/user/{id}",
                    "method": "GET",
                    "category": "用户管理",
                    "status": "done"
                },
                "document": "获取用户信息的API接口",
                "distance": 0.2
            }
        ]
        
        mock_client_instance = Mock()
        mock_client_instance.search_apis.return_value = mock_search_results
        mock_chroma_client.return_value = mock_client_instance
        
        with patch('src.vector_db.manager.EmbeddingClient'):
            manager = VectorDBManager()
            results = manager.search_apis("用户信息", 5)
            
            assert len(results) == 1
            assert results[0]["api_id"] == "1"
            assert results[0]["title"] == "获取用户信息"
            assert results[0]["similarity_score"] == 0.8  # 1 - 0.2
    
    @patch('src.vector_db.manager.ChromaDBClient')
    def test_get_database_info(self, mock_chroma_client):
        """测试获取数据库信息"""
        mock_client_instance = Mock()
        mock_client_instance.get_collection_info.return_value = {
            "name": "rest_apis",
            "count": 10,
            "persist_directory": "/tmp/chroma_db"
        }
        mock_chroma_client.return_value = mock_client_instance
        
        with patch('src.vector_db.manager.EmbeddingClient'):
            manager = VectorDBManager()
            info = manager.get_database_info()
            
            assert info["status"] == "connected"
            assert info["api_count"] == 10
            assert info["collection_name"] == "rest_apis"
    
    @patch('src.vector_db.manager.ChromaDBClient')
    def test_clear_database(self, mock_chroma_client):
        """测试清空数据库"""
        mock_client_instance = Mock()
        mock_client_instance.clear_collection.return_value = True
        mock_chroma_client.return_value = mock_client_instance
        
        with patch('src.vector_db.manager.EmbeddingClient'):
            manager = VectorDBManager()
            result = manager.clear_database()
            
            assert result is True
            mock_client_instance.clear_collection.assert_called_once()
    
    def test_empty_apis_list(self):
        """测试空API列表"""
        with patch('src.vector_db.manager.ChromaDBClient'), \
             patch('src.vector_db.manager.EmbeddingClient'):
            manager = VectorDBManager()
            result = manager.add_apis([])
            
            assert result is True  # 空列表应该返回True
    
    @patch('src.vector_db.manager.ChromaDBClient')
    def test_search_empty_query(self, mock_chroma_client):
        """测试空查询"""
        mock_client_instance = Mock()
        mock_client_instance.search_apis.return_value = []
        mock_chroma_client.return_value = mock_client_instance
        
        with patch('src.vector_db.manager.EmbeddingClient'):
            manager = VectorDBManager()
            results = manager.search_apis("", 5)
            
            assert len(results) == 0
