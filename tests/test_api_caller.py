"""
API调用器测试
"""

import pytest
from unittest.mock import Mock, patch
import httpx
from src.agent.api_caller import ApiCaller


class TestApiCaller:
    """API调用器测试"""
    
    @pytest.fixture
    def sample_api_data(self):
        """示例API数据"""
        return {
            "_id": "1",
            "title": "获取用户信息",
            "path": "/api/user/{id}",
            "method": "GET",
            "project_id": "123",
            "req_params": [
                {"name": "id", "desc": "用户ID"}
            ],
            "req_query": [
                {"name": "include_profile", "desc": "是否包含详细信息", "required": False}
            ],
            "req_headers": [
                {"name": "Authorization", "value": "Bearer token"}
            ]
        }
    
    @pytest.fixture
    def api_caller(self):
        """API调用器实例"""
        return ApiCaller()
    
    def test_build_url_with_path_params(self, api_caller, sample_api_data):
        """测试构建URL（包含路径参数）"""
        parameters = {"id": "123"}
        base_url = "https://api.example.com"
        
        url = api_caller._build_url(sample_api_data, parameters, base_url)
        
        assert url == "https://api.example.com/api/user/123"
    
    def test_build_url_without_base_url(self, api_caller, sample_api_data):
        """测试构建URL（无基础URL）"""
        parameters = {"id": "123"}
        
        url = api_caller._build_url(sample_api_data, parameters, None)
        
        assert url == "/api/user/123"
    
    def test_build_headers(self, api_caller, sample_api_data):
        """测试构建请求头"""
        parameters = {"header_Custom": "custom_value"}
        
        headers = api_caller._build_headers(sample_api_data, parameters)
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer token"
        assert headers["Custom"] == "custom_value"
        assert headers["Content-Type"] == "application/json"  # 默认头
    
    def test_build_query_params(self, api_caller, sample_api_data):
        """测试构建查询参数"""
        parameters = {"include_profile": "true", "other_param": "value"}
        
        query_params = api_caller._build_query_params(sample_api_data, parameters)
        
        assert query_params["include_profile"] == "true"
        assert "other_param" not in query_params  # 不在API定义中的参数不应包含
    
    def test_build_request_body_json(self, api_caller):
        """测试构建JSON请求体"""
        api_data = {
            "req_body_type": "json",
            "req_body_other": '{"name": "test", "age": 25}'
        }
        parameters = {"request_body": '{"name": "John", "age": 30}'}
        
        body = api_caller._build_request_body(api_data, parameters)
        
        assert body == {"name": "John", "age": 30}
    
    def test_build_request_body_form(self, api_caller):
        """测试构建表单请求体"""
        api_data = {
            "req_body_type": "form",
            "req_body_form": [
                {"name": "username", "desc": "用户名"},
                {"name": "password", "desc": "密码"}
            ]
        }
        parameters = {"username": "john", "password": "secret"}
        
        body = api_caller._build_request_body(api_data, parameters)
        
        assert body == {"username": "john", "password": "secret"}
    
    @patch('httpx.Client.request')
    def test_call_api_success(self, mock_request, api_caller, sample_api_data):
        """测试成功的API调用"""
        # Mock成功响应
        mock_response = Mock()
        mock_response.is_success = True
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.json.return_value = {"id": 123, "name": "John"}
        mock_request.return_value = mock_response
        
        parameters = {"id": "123"}
        base_url = "https://api.example.com"
        
        result = api_caller.call_api(sample_api_data, parameters, base_url)
        
        assert result["success"] is True
        assert result["status_code"] == 200
        assert result["response"] == {"id": 123, "name": "John"}
        assert result["error"] is None
    
    @patch('httpx.Client.request')
    def test_call_api_failure(self, mock_request, api_caller, sample_api_data):
        """测试失败的API调用"""
        # Mock失败响应
        mock_response = Mock()
        mock_response.is_success = False
        mock_response.status_code = 404
        mock_response.reason_phrase = "Not Found"
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.json.return_value = {"error": "User not found"}
        mock_request.return_value = mock_response
        
        parameters = {"id": "999"}
        base_url = "https://api.example.com"
        
        result = api_caller.call_api(sample_api_data, parameters, base_url)
        
        assert result["success"] is False
        assert result["status_code"] == 404
        assert result["error"] == "HTTP 404: Not Found"
    
    @patch('httpx.Client.request')
    def test_call_api_exception(self, mock_request, api_caller, sample_api_data):
        """测试API调用异常"""
        # Mock异常
        mock_request.side_effect = httpx.RequestError("Connection failed")
        
        parameters = {"id": "123"}
        base_url = "https://api.example.com"
        
        result = api_caller.call_api(sample_api_data, parameters, base_url)
        
        assert result["success"] is False
        assert "Connection failed" in result["error"]
        assert result["status_code"] is None
    
    def test_process_response_json(self, api_caller):
        """测试处理JSON响应"""
        mock_response = Mock()
        mock_response.is_success = True
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.json.return_value = {"message": "success"}
        
        result = api_caller._process_response(mock_response)
        
        assert result["success"] is True
        assert result["status_code"] == 200
        assert result["response"] == {"message": "success"}
    
    def test_process_response_text(self, api_caller):
        """测试处理文本响应"""
        mock_response = Mock()
        mock_response.is_success = True
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "text/plain"}
        mock_response.text = "Plain text response"
        
        result = api_caller._process_response(mock_response)
        
        assert result["success"] is True
        assert result["response"] == "Plain text response"
