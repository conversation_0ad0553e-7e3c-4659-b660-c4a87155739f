import asyncio
from claude_code_sdk import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ode<PERSON><PERSON><PERSON>,AssistantMessage,TextBlock

async def main():
    # 设置系统的环境变量
    import os
    os.environ["ANTHROPIC_BASE_URL"] = "http://127.0.0.1:8100/"
    os.environ["ANTHROPIC_AUTH_TOKEN"] = "NO_KEY"
    os.environ["ANTHROPIC_MODEL"] = "qwen3-32b"
    os.environ["ANTHROPIC_DEFAULT_SONNET_MODEL"] = "qwen3-32b"
    os.environ["ANTHROPIC_DEFAULT_OPUS_MODEL"] = "qwen3-32b"
    os.environ["ANTHROPIC_SMALL_FAST_MODEL"] = "qwen3-32b"
    async with ClaudeSDKClient(
        options=ClaudeCodeOptions(
            system_prompt="始终用中文回复",
            max_turns=2,
            cwd="/mnt/d/aitools/restAgent/claude-demo",
            permission_mode = "bypassPermissions"
        )
    ) as client:
        # Send the query
        await client.query(
            "介绍自己"
        )

        # Stream the response
        async for message in client.receive_response():
            # if isinstance(message, AssistantMessage):
            #     for block in message.content:
            #         if isinstance(block, TextBlock):
            #             print(f"Claude: {block.text}")
            if hasattr(message, 'content'):
                # Print streaming content as it arrives
                for block in message.content:
                    if hasattr(block, 'text'):
                        print(block.text, end='', flush=True)

if __name__ == "__main__":
    asyncio.run(main())