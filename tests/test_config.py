"""
配置模块测试
"""

import pytest
import os
import tempfile
import yaml
from src.config.settings import Settings, get_settings


class TestSettings:
    """配置设置测试"""
    
    def test_default_settings(self):
        """测试默认配置"""
        settings = Settings()
        
        assert settings.yapi.base_url == "http://localhost:3000"
        assert settings.llm.provider == "openai"
        assert settings.vector_db.type == "chromadb"
        assert settings.web.host == "0.0.0.0"
        assert settings.web.port == 8000
    
    def test_from_yaml(self):
        """测试从YAML文件加载配置"""
        # 创建临时配置文件
        config_data = {
            "yapi": {
                "base_url": "http://test.com",
                "username": "test_user",
                "password": "test_pass",
                "project_id": "123"
            },
            "llm": {
                "provider": "openai",
                "model_name": "gpt-4",
                "temperature": 0.5
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_file = f.name
        
        try:
            settings = Settings.from_yaml(temp_file)
            
            assert settings.yapi.base_url == "http://test.com"
            assert settings.yapi.username == "test_user"
            assert settings.llm.model_name == "gpt-4"
            assert settings.llm.temperature == 0.5
            
        finally:
            os.unlink(temp_file)
    
    def test_nonexistent_yaml(self):
        """测试不存在的YAML文件"""
        settings = Settings.from_yaml("nonexistent.yaml")
        
        # 应该返回默认配置
        assert settings.yapi.base_url == "http://localhost:3000"
        assert settings.llm.provider == "openai"


def test_get_settings():
    """测试获取设置单例"""
    settings1 = get_settings()
    settings2 = get_settings()
    
    # 应该是同一个实例
    assert settings1 is settings2
