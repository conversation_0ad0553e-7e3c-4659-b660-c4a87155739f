# RestAgent - 调用REST API工具的智能体应用

RestAgent是一个基于向量数据库和大模型的智能体应用，能够自动调用REST API接口来完成用户任务。

## 功能特性

### 1. 向量数据库
- 集成YApi接口管理工具，自动获取项目REST接口
- 使用ChromaDB进行接口向量化存储
- 支持接口语义搜索和匹配

### 2. 智能助手Agent
- 自然语言理解用户需求
- 智能匹配最相关的REST接口
- 自动调用接口并处理结果
- 支持多轮对话和工具链调用

### 3. Web UI界面
- 友好的对话界面
- 向量搜索功能
- API调用测试工具
- 支持图表渲染

### 4. 系统配置
- 灵活的配置管理
- 支持多种大模型提供商
- REST服务授权管理

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置设置

复制配置文件并修改：
```bash
cp .env.example .env
```

编辑 `config.yaml` 文件，配置YApi、大模型等信息。

### 3. 启动应用

```bash
python main.py
```

访问 http://localhost:8000 查看Web界面。

## 项目结构

```
restAgent/
├── src/
│   ├── vector_db/      # 向量数据库模块
│   ├── yapi_client/    # YApi客户端
│   ├── agent/          # 智能助手核心
│   ├── web_ui/         # Web界面
│   └── config/         # 配置管理
├── tests/              # 测试文件
├── static/             # 静态资源
├── templates/          # 模板文件
├── debug/              # 调试文件
├── config.yaml         # 主配置文件
├── requirements.txt    # 依赖包
└── main.py            # 主入口
```

## 开发指南

### 运行测试

```bash
pytest tests/
```

### 开发模式

```bash
python main.py
```

应用将在调试模式下运行，支持热重载。

## 许可证

MIT License
